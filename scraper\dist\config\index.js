// scraper/config/index.ts
// Configuration for scraper service
import dotenv from "dotenv";
dotenv.config();
export const config = {
    // Database
    database: {
        url: process.env.DATABASE_URL || "",
    },
    // Redis
    redis: {
        url: process.env.REDIS_URL || "redis://localhost:6379",
    },
    // Scraper settings
    scraper: {
        maxWorkers: parseInt(process.env.SCRAPER_MAX_WORKERS || "3"),
        batchSize: parseInt(process.env.SCRAPER_BATCH_SIZE || "5"),
        concurrency: parseInt(process.env.SCRAPER_CONCURRENCY || "2"),
        headless: process.env.NODE_ENV === "production",
        maxJobsPerRun: parseInt(process.env.SCRAPER_MAX_JOBS || "1000"),
    },
    // Job details scraper
    jobDetails: {
        batchSize: parseInt(process.env.JOB_DETAILS_BATCH_SIZE || "3"),
        maxConcurrent: parseInt(process.env.JOB_DETAILS_MAX_CONCURRENT || "1"),
        maxJobsPerRun: parseInt(process.env.JOB_DETAILS_MAX_JOBS || "200"),
    },
    // System
    system: {
        environment: process.env.NODE_ENV || "development",
        isProduction: process.env.NODE_ENV === "production",
    },
    // Proxy settings
    proxy: {
        enabled: process.env.PROXY_ENABLED === "true",
        url: process.env.PROXY_URL || "",
    },
};
