// cron/lib/cleanJobData.ts
import { PrismaClient } from "@prisma/client";
/**
 * Utility functions to clean and standardize job data before saving to database
 */
const prisma = new PrismaClient();
// Cache for city names is defined below
/**
 * Clean job title by removing common prefixes, suffixes, and standardizing format
 * @param title Raw job title
 * @returns Cleaned job title
 */
export function cleanJobTitle(title) {
    if (!title)
        return "";
    // Trim and standardize whitespace
    let cleaned = title.trim().replace(/\s+/g, " ");
    // Remove job ID patterns at the end of titles (more comprehensive)
    cleaned = cleaned.replace(/,?\s+Job\s+Id\s+\d+$/i, "");
    cleaned = cleaned.replace(/,?\s+Job\s+ID:?\s+\d+$/i, "");
    cleaned = cleaned.replace(/,?\s+ID:?\s+\d+$/i, "");
    cleaned = cleaned.replace(/,?\s+#\d+$/i, "");
    // Remove city, state patterns at the end of titles (more comprehensive)
    // Match patterns like "Alhambra, CA" or "Alhambra, Ca" or "New York, NY"
    cleaned = cleaned.replace(/,?\s+[A-Za-z\s]+,\s*[A-Za-z]{2}$/i, "");
    // Handle the specific pattern seen in the example
    // "4-H Youth Development Community Education Specialist Alhambra, Ca, Job Id 76467"
    cleaned = cleaned.replace(/^(.+?)\s+[A-Za-z\s]+,\s*[A-Za-z]{2},\s*Job\s+Id\s+\d+$/i, "$1");
    // Handle special case for titles starting with numbers and acronyms like "4-H"
    cleaned = cleaned.replace(/^(\d+)-([A-Za-z])/i, (_match, num, letter) => {
        return `${num}-${letter.toUpperCase()}`;
    });
    // Handle Roman numerals (I, II, III, IV, V, etc.)
    cleaned = cleaned.replace(/\b([I|V|X]+)-([I|V|X]+)\b/g, (match) => {
        return match.toUpperCase();
    });
    // Handle 3D/2D prefixes
    cleaned = cleaned.replace(/\b(\d+)[dD]\b/g, (match) => {
        return match.toUpperCase();
    });
    // Handle common acronyms in job titles
    const commonAcronyms = [
        "MRI",
        "CT",
        "PET",
        "EEG",
        "ECG",
        "ICU",
        "ER",
        "OR",
        "RN",
        "LPN",
        "CNA",
        "EMT",
        "PA",
        "NP",
        "MD",
        "DO",
        "PT",
        "OT",
        "ST",
        "RT",
        "HC",
        "HR",
        "PR",
        "IT",
        "QA",
        "QC",
        "CNC",
        "CAD",
        "CAM",
        "CRM",
        "ERP",
        "SAP",
        "API",
        "UI",
        "UX",
        "VP",
        "CEO",
        "CFO",
        "CTO",
        "COO",
        "CIO",
        "CMO",
        "CHRO",
    ];
    // Create a regex pattern for all acronyms
    const acronymPattern = new RegExp(`\\b(${commonAcronyms.join("|")})\\d*\\b`, "gi");
    // Replace acronyms with uppercase versions
    cleaned = cleaned.replace(acronymPattern, (match) => {
        // Extract any numbers that might be part of the match
        const letters = match.replace(/\d/g, "");
        const numbers = match.replace(/[^\d]/g, "");
        // Return the uppercase acronym with any numbers
        return letters.toUpperCase() + numbers;
    });
    // General pattern for "Job Title (Job Title)-with Requirements"
    const redundantTitleWithReqsRegex = /(\w+(?:\s+\w+)+)\s*\(\1\)\s*-\s*with\s+.*$/i;
    if (redundantTitleWithReqsRegex.test(cleaned)) {
        cleaned = cleaned.replace(redundantTitleWithReqsRegex, "$1");
    }
    // General pattern for "Job Title (Abbreviation) - with Requirements"
    const titleWithAbbrAndReqsRegex = /(\w+(?:\s+\w+)+)\s*\(([A-Z]{2,})\)\s*-\s*with\s+.*$/i;
    if (titleWithAbbrAndReqsRegex.test(cleaned)) {
        return cleaned.replace(titleWithAbbrAndReqsRegex, "$1 ($2)");
    }
    // General pattern for "Job Title (Abbreviation) - requires Requirements"
    const titleWithAbbrAndRequiresRegex = /(\w+(?:\s+\w+)+)\s*\(([A-Z]{2,})\)\s*-\s*requires\s+.*$/i;
    if (titleWithAbbrAndRequiresRegex.test(cleaned)) {
        return cleaned.replace(titleWithAbbrAndRequiresRegex, "$1 ($2)");
    }
    // General pattern for "Job Title (Abbreviation) - required Requirements"
    const titleWithAbbrAndRequiredRegex = /(\w+(?:\s+\w+)+)\s*\(([A-Z]{2,})\)\s*-\s*required\s+.*$/i;
    if (titleWithAbbrAndRequiredRegex.test(cleaned)) {
        return cleaned.replace(titleWithAbbrAndRequiredRegex, "$1 ($2)");
    }
    // Pattern for "Job Title Job with Company in Location"
    const jobWithCompanyRegex = /(.*?)\s+Job\s+with\s+.*?\s+in\s+.*$/i;
    if (jobWithCompanyRegex.test(cleaned)) {
        return cleaned.replace(jobWithCompanyRegex, "$1");
    }
    // Pattern for "Job Title Job in Location"
    const jobInLocationRegex = /(.*?)\s+Job\s+in\s+.*$/i;
    if (jobInLocationRegex.test(cleaned)) {
        return cleaned.replace(jobInLocationRegex, "$1");
    }
    // Pattern for "Job Title in City, State"
    const titleInCityStateRegex = /(.*?)\s+in\s+[\w\s]+,\s*[A-Za-z]{2}$/i;
    if (titleInCityStateRegex.test(cleaned)) {
        return cleaned.replace(titleInCityStateRegex, "$1");
    }
    // Pattern for "Job Title In City, State" (exact match for capitalized "In")
    if (cleaned.includes(" In ")) {
        const titleCapInCityStateRegex = new RegExp("(.*?)\\s+In\\s+[\\w\\s]+,\\s*[A-Za-z]{2}$");
        if (titleCapInCityStateRegex.test(cleaned)) {
            return cleaned.replace(titleCapInCityStateRegex, "$1");
        }
    }
    // Pattern for "Job Title (Specialty) City, State"
    const titleSpecialtyLocationRegex = /(.*?)\s*\([^)]+\)\s+[\w\s]+,\s*[A-Za-z]{2}$/i;
    if (titleSpecialtyLocationRegex.test(cleaned)) {
        // Extract the job title and specialty
        const match = cleaned.match(/(.*?)\s*\(([^)]+)\)\s+[\w\s]+,\s*[A-Za-z]{2}$/i);
        if (match && match.length >= 3) {
            return `${match[1]} (${match[2]})`;
        }
    }
    // Pattern for "Job Title-Suffix" (like "Lead Sales Associate-Ft")
    const titleSuffixRegex = /(.*?)-\s*(Ft|Pt|Seasonal|School Based|Remote|Remotely)$/i;
    if (titleSuffixRegex.test(cleaned)) {
        return cleaned.replace(titleSuffixRegex, "$1");
    }
    // Pattern for "P T Job Title" (Part Time prefix)
    const ptPrefixRegex = /^P\s*T\s+(.*)$/i;
    if (ptPrefixRegex.test(cleaned)) {
        return cleaned.replace(ptPrefixRegex, "$1");
    }
    // Pattern for "Temporary/Seasonal Job Title"
    const tempPrefixRegex = /^(Temporary|Temp|Seasonal)\s+(.*)$/i;
    if (tempPrefixRegex.test(cleaned)) {
        return cleaned.replace(tempPrefixRegex, "$2");
    }
    // Pattern for "Live-in Job Title"
    const liveInPrefixRegex = /^Live-in\s+(.*)$/i;
    if (liveInPrefixRegex.test(cleaned)) {
        return cleaned.replace(liveInPrefixRegex, "$1");
    }
    // Pattern for "Job Title Door-to-Door, Remote, etc."
    const doorToDoorSuffixRegex = /(.*?)\s+Door-to-Door.*$/i;
    if (doorToDoorSuffixRegex.test(cleaned)) {
        return cleaned.replace(doorToDoorSuffixRegex, "$1");
    }
    // Pattern for "Job Title Work from Home Remotely"
    const workFromHomeSuffixRegex = /(.*?)\s+Work\s+from\s+Home.*$/i;
    if (workFromHomeSuffixRegex.test(cleaned)) {
        return cleaned.replace(workFromHomeSuffixRegex, "$1");
    }
    // Remove HTML entities and convert to plain text
    cleaned = cleaned
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
    // Remove special characters except for common punctuation
    cleaned = cleaned.replace(/[^a-zA-Z0-9\s\-&.,()]/g, " ").replace(/\s+/g, " ");
    // Remove "hiring" or "now hiring" prefix
    cleaned = cleaned.replace(/^(now\s+)?hiring(\s+for)?:?\s*/i, "");
    // Remove "job" or "position" suffix
    cleaned = cleaned.replace(/\s+(job|position)$/i, "");
    // Remove location in parentheses at the end
    cleaned = cleaned.replace(/\s+\([^)]*\)$/i, "");
    // Special case for "Teaching Assistant (Teaching Assistant)"
    const redundantParensRegex = new RegExp("(\\w+\\s+\\w+)\\s*\\(\\1\\)", "i");
    if (redundantParensRegex.test(cleaned)) {
        cleaned = cleaned.replace(redundantParensRegex, "$1");
    }
    // Special case for "Project Manager (PM)" - don't remove abbreviations in parentheses
    // that are abbreviations of the main title
    cleaned = cleaned.replace(/\s*\(([A-Z]{2,})\)/g, (match, abbr) => {
        // Check if this is an abbreviation of words in the title
        const titleWithoutParens = cleaned.replace(match, "").trim();
        const words = titleWithoutParens.split(/\s+/);
        // Check if the abbreviation could be formed from the first letters of words
        const possibleAbbr = words
            .map((word) => word[0])
            .join("")
            .toUpperCase();
        if (possibleAbbr === abbr) {
            // This is likely an abbreviation of the title, so keep it
            return match;
        }
        // Otherwise check if it's redundant
        if (titleWithoutParens.toLowerCase().includes(abbr.toLowerCase())) {
            return "";
        }
        return match;
    });
    // Handle other parenthetical expressions that might be redundant
    cleaned = cleaned.replace(/\s*\(([^)]*)\)/g, (match, contents) => {
        // If the content in parentheses is already in the title, remove it
        const titleWithoutParens = cleaned.replace(match, "");
        if (titleWithoutParens.toLowerCase().includes(contents.toLowerCase())) {
            return "";
        }
        // Otherwise keep the parentheses
        return match;
    });
    // Remove suffixes that indicate requirements
    cleaned = cleaned
        .replace(/\s*-\s*with\s+.*$/i, "") // Remove "-with ..." suffix
        .replace(/\s*-\s*requires\s+.*$/i, "") // Remove "-requires ..." suffix
        .replace(/\s*-\s*required\s+.*$/i, ""); // Remove "-required ..." suffix
    // Remove "- company name" suffix
    cleaned = cleaned.replace(/\s+-\s+[^-]+$/i, "");
    // Remove "at company name" suffix
    cleaned = cleaned.replace(/\s+at\s+[^,]+$/i, "");
    // Remove "in location" suffix
    cleaned = cleaned.replace(/\s+in\s+[^,]+$/i, "");
    // Remove "in City, State" suffix
    cleaned = cleaned.replace(/\s+in\s+[\w\s]+,\s*[A-Za-z]{2}$/i, "");
    // Remove "In City, State" suffix (capitalized "In")
    if (cleaned.includes(" In ")) {
        cleaned = cleaned.replace(/\s+In\s+[\w\s]+,\s*[A-Za-z]{2}$/, "");
    }
    // Remove "remote" suffix
    cleaned = cleaned.replace(/\s+remote$/i, "");
    // Remove state name at the end
    cleaned = cleaned.replace(/,\s+Southern\s+California$/i, "");
    cleaned = cleaned.replace(/,\s+California$/i, "");
    cleaned = cleaned.replace(/\s+Southern\s+California$/i, "");
    cleaned = cleaned.replace(/\s+California$/i, "");
    // Special case for "Key Account Manager, Southern California"
    if (cleaned === "Key Account Manager, Southern") {
        cleaned = "Key Account Manager";
    }
    // Remove "full-time" or "part-time" suffix
    cleaned = cleaned.replace(/\s+(full|part)[-\s]time$/i, "");
    // Remove employment type indicators
    cleaned = cleaned.replace(/\s+Permanent\b/i, "");
    cleaned = cleaned.replace(/\s+Temporary\b/i, "");
    cleaned = cleaned.replace(/\s+Seasonal\b/i, "");
    cleaned = cleaned.replace(/\s+Contract\b/i, "");
    cleaned = cleaned.replace(/\s+Parttime\b/i, "");
    // Remove shift information
    cleaned = cleaned.replace(/\s+(Full|Part)\s+Time\s+\d+\s+Hour\s+(Day|Night|Evening|Rotating)\s+Shift\b/i, "");
    cleaned = cleaned.replace(/\s+\d+\s+Hour\s+(Day|Night|Evening|Rotating)\s+Shift\b/i, "");
    cleaned = cleaned.replace(/\s+(Day|Night|Evening|Rotating)\s+Shift\b/i, "");
    cleaned = cleaned.replace(/\s+(1st|2nd|3rd)\s+Shift\b/i, "");
    // Remove common job level indicators that appear at the end
    cleaned = cleaned.replace(/\s+(I|II|III|IV|V)\s*$/i, "");
    // Remove common job level indicators with hyphens
    cleaned = cleaned.replace(/\s+-\s+(I|II|III|IV|V)\s*$/i, "");
    // Remove common job level indicators with Roman numerals
    cleaned = cleaned.replace(/\s+(Junior|Senior|Lead|Principal|Staff|Associate|Entry[-\s]Level)\s*$/i, "");
    // Remove role indicators
    cleaned = cleaned.replace(/\s+Advanced\s+Provider\b/i, "");
    cleaned = cleaned.replace(/\s+Trainee\b/i, "");
    cleaned = cleaned.replace(/\s+\d+hr\b/i, ""); // Remove hourly rate like "18hr"
    // Remove experience requirements
    cleaned = cleaned.replace(/\s+Experience\s+Required\b/i, "");
    cleaned = cleaned.replace(/\s+\d+\+?\s+Years?\s+Experience\b/i, ""); // "5+ Years Experience"
    // Be more careful with "X Experience" pattern - only remove if X is a specific skill
    const skillsRegex = /(\s+)(Hvac|Plumbing|Construction|Programming|Coding|Design|Marketing|Sales|Customer Service|Accounting|Finance)\s+Experience\b/i;
    cleaned = cleaned.replace(skillsRegex, "$1");
    // Special case for "Construction Experience"
    cleaned = cleaned.replace(/\s+Construction\s+Experience\b/i, "");
    // Remove "immediate start" or "start immediately" suffix
    cleaned = cleaned.replace(/\s+(immediate\s+start|start\s+immediately)$/i, "");
    // Remove "apply now" suffix
    cleaned = cleaned.replace(/\s+apply\s+now$/i, "");
    // Remove "new" prefix
    cleaned = cleaned.replace(/^new\s+/i, "");
    // Remove "urgent" prefix
    cleaned = cleaned.replace(/^urgent:?\s*/i, "");
    // Remove "featured" prefix
    cleaned = cleaned.replace(/^featured:?\s*/i, "");
    // Remove "top" prefix
    cleaned = cleaned.replace(/^top:?\s*/i, "");
    // Remove ID prefixes (like "Pr 1428854 Dispatcher" or "ID: 12345 Software Engineer")
    cleaned = cleaned
        .replace(/^(pr|id|ref|req|requisition|job|position)[-\s]*[#:]?\s*[\d\w-]+\s+/i, "")
        .replace(/^\d{5,}[-\w]*\s+/i, "") // Remove 5+ digit numbers at the start, with optional suffix
        .replace(/^[a-z]{1,3}[-\s]+\d{5,}[-\w]*\s+/i, ""); // Remove short code + number (like "Pr 1428854")
    // Handle other code prefixes
    // Check if there's a space in the title
    let codeFirstSpace = cleaned.indexOf(" ");
    if (codeFirstSpace > 0) {
        const firstPart = cleaned.substring(0, codeFirstSpace);
        const restOfTitle = cleaned.substring(codeFirstSpace + 1);
        // Case 1: CONST123 pattern (all caps with numbers)
        if (/^[A-Z0-9]{3,}$/.test(firstPart)) {
            cleaned = restOfTitle;
        }
        // Case 2: CONST123: pattern (all caps with numbers followed by colon)
        else if (/^[A-Z0-9]{3,}:$/.test(firstPart)) {
            cleaned = restOfTitle;
        }
    }
    // Catch any remaining all-caps constants at the beginning
    cleaned = cleaned.replace(/^[A-Z0-9]{3,}([:\s]+|$)/g, "");
    // Define a mapping of common abbreviations to their full forms
    const abbreviationMap = {
        // Position levels
        sr: "Senior",
        jr: "Junior",
        mgr: "Manager",
        asst: "Assistant",
        assoc: "Associate",
        dir: "Director",
        exec: "Executive",
        vp: "VP",
        // Technical roles
        eng: "Engineer",
        dev: "Developer",
        admin: "Administrator",
        "sys admin": "System Administrator",
        qa: "Quality Assurance",
        dba: "Database Administrator",
        swe: "Software Engineer",
        pm: "Project Manager",
        // Departments
        it: "IT",
        hr: "HR",
        mktg: "Marketing",
        fin: "Finance",
        acct: "Accounting",
        ops: "Operations",
        biz: "Business",
        "biz dev": "Business Development",
        // Healthcare
        rn: "Registered Nurse",
        lpn: "Licensed Practical Nurse",
        cna: "Certified Nursing Assistant",
        md: "Medical Doctor",
        pa: "Physician Assistant",
        pt: "Physical Therapist",
        ot: "Occupational Therapist",
        slp: "Speech Language Pathologist",
        np: "Nurse Practitioner",
        crna: "Certified Registered Nurse Anesthetist",
        rt: "Respiratory Therapist",
        st: "Speech Therapist",
        mt: "Massage Therapist",
        // Education
        prof: "Professor",
        ta: "Teaching Assistant",
        sped: "Special Education",
        // Other
        mgmt: "Management",
        coord: "Coordinator",
        spec: "Specialist",
        tech: "Technician",
        rep: "Representative",
        cust: "Customer",
        svc: "Service",
        "cust svc": "Customer Service",
        "acct exec": "Account Executive",
    };
    // Apply the abbreviation mapping
    // First, extract and save parenthetical content
    const parentheticalContent = [];
    cleaned = cleaned.replace(/\(([^)]*)\)/g, (_match, content) => {
        const placeholder = `__PAREN_${parentheticalContent.length}__`;
        parentheticalContent.push(content);
        return `(${placeholder})`;
    });
    // Now apply abbreviation mapping to the main text
    for (const [abbr, fullForm] of Object.entries(abbreviationMap)) {
        // Create a regex that matches the abbreviation as a whole word,
        // optionally followed by a period, and with optional whitespace
        const regex = new RegExp(`\\b${abbr}\.?\\b`, "i");
        // Special case for abbreviations at the beginning of the title
        if (cleaned.match(new RegExp(`^${abbr}\\b`, "i"))) {
            // Only replace if there's a space after the abbreviation
            cleaned = cleaned.replace(new RegExp(`^${abbr}(\\s+)`, "i"), `${fullForm}$1`);
        }
        else if (regex.test(cleaned)) {
            // For other cases, ensure we're replacing a whole word
            cleaned = cleaned.replace(regex, fullForm);
        }
    }
    // Restore parenthetical content
    for (let i = 0; i < parentheticalContent.length; i++) {
        cleaned = cleaned.replace(`__PAREN_${i}__`, parentheticalContent[i]);
    }
    // Standardize dashes: convert dash with spaces around it to space
    cleaned = cleaned.replace(/\s+-\s+/g, " ");
    // Convert multiple dashes to single dash
    cleaned = cleaned.replace(/--+/g, "-");
    // Ensure no spaces around remaining dashes
    cleaned = cleaned.replace(/\s+-/g, "-").replace(/-\s+/g, "-");
    // Convert to lowercase first to ensure consistent capitalization
    cleaned = cleaned.toLowerCase();
    // Capitalize first letter of each word except for certain prepositions and articles
    cleaned = cleaned.replace(/\b\w+\b/g, (word) => {
        const lowerCaseWords = [
            "a",
            "an",
            "the",
            "and",
            "but",
            "or",
            "for",
            "nor",
            "on",
            "at",
            "to",
            "from",
            "by",
            "with",
            "in",
            "of",
        ];
        // Don't change case for words that contain numbers (like 3D, 911, etc.)
        if (/\d/.test(word)) {
            return word;
        }
        return lowerCaseWords.includes(word.toLowerCase()) &&
            !cleaned.startsWith(word)
            ? word.toLowerCase()
            : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    });
    // Ensure specific terms are properly capitalized
    cleaned = cleaned
        .replace(/\bIt\b/g, "IT")
        .replace(/\bHr\b/g, "HR")
        .replace(/\bCeo\b/g, "CEO")
        .replace(/\bCfo\b/g, "CFO")
        .replace(/\bCto\b/g, "CTO")
        .replace(/\bCoo\b/g, "COO")
        .replace(/\bCio\b/g, "CIO")
        .replace(/\bCmo\b/g, "CMO")
        .replace(/\bVp\b/g, "VP")
        .replace(/\bUi\b/g, "UI")
        .replace(/\bUx\b/g, "UX")
        .replace(/\bApi\b/g, "API")
        .replace(/\bSql\b/g, "SQL")
        .replace(/\bPhp\b/g, "PHP")
        .replace(/\bAws\b/g, "AWS")
        .replace(/\bDevops\b/g, "DevOps")
        .replace(/\bIos\b/g, "iOS")
        .replace(/\bAndroid\b/g, "Android");
    // Trim again and return
    return cleaned.trim();
}
/**
 * Clean company name by removing common suffixes and standardizing format
 * @param company Raw company name
 * @returns Cleaned company name
 */
export function cleanCompanyName(company) {
    if (!company || company.toLowerCase() === "unknown")
        return "Unknown";
    // Trim and standardize whitespace
    let cleaned = company.trim().replace(/\s+/g, " ");
    // Remove HTML entities and convert to plain text
    cleaned = cleaned
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
    // Remove special characters except for common punctuation
    cleaned = cleaned.replace(/[^a-zA-Z0-9\s\-&.,()]/g, " ").replace(/\s+/g, " ");
    // Remove location in parentheses
    cleaned = cleaned.replace(/\s+\([^)]*\)$/i, "");
    // Remove common legal suffixes
    cleaned = cleaned.replace(/\s+(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)$/i, "");
    // Remove "- location" suffix
    cleaned = cleaned.replace(/\s+-\s+[^-]+$/i, "");
    // Remove "in location" suffix
    cleaned = cleaned.replace(/\s+in\s+[^,]+$/i, "");
    // Remove "hiring" or "now hiring" prefix
    cleaned = cleaned.replace(/^(now\s+)?hiring(\s+for)?:?\s*/i, "");
    // Remove "jobs at" prefix
    cleaned = cleaned.replace(/^jobs\s+at\s+/i, "");
    // Remove "careers at" prefix
    cleaned = cleaned.replace(/^careers\s+at\s+/i, "");
    // Remove trailing commas and periods
    cleaned = cleaned.replace(/[,.]$/g, "");
    // Convert to lowercase first to ensure consistent capitalization
    cleaned = cleaned.toLowerCase();
    // Capitalize first letter of each word (except common words)
    cleaned = cleaned.replace(/\b\w+\b/g, (word) => {
        const lowerCaseWords = [
            "a",
            "an",
            "and",
            "or",
            "the",
            "of",
            "in",
            "on",
            "at",
            "by",
            "for",
            "with",
            "to",
            "from",
        ];
        return lowerCaseWords.includes(word) && !cleaned.startsWith(word)
            ? word
            : word.charAt(0).toUpperCase() + word.slice(1);
    });
    // Fix specific company name capitalizations
    cleaned = cleaned
        .replace(/\bIbm\b/g, "IBM")
        .replace(/\bMicrosoft\b/g, "Microsoft")
        .replace(/\bGoogle\b/g, "Google")
        .replace(/\bAmazon\b/g, "Amazon")
        .replace(/\bApple\b/g, "Apple")
        .replace(/\bFacebook\b/g, "Facebook")
        .replace(/\bMeta\b/g, "Meta")
        .replace(/\bNetflix\b/g, "Netflix")
        .replace(/\bOracle\b/g, "Oracle")
        .replace(/\bSap\b/g, "SAP")
        .replace(/\bHp\b/g, "HP")
        .replace(/\bDell\b/g, "Dell")
        .replace(/\bCisco\b/g, "Cisco")
        .replace(/\bAdobe\b/g, "Adobe")
        .replace(/\bSalesforce\b/g, "Salesforce")
        .replace(/\bVmware\b/g, "VMware")
        .replace(/\bEbay\b/g, "eBay")
        .replace(/\bPaypal\b/g, "PayPal")
        .replace(/\bAt&t\b/g, "AT&T")
        .replace(/\bT-mobile\b/g, "T-Mobile")
        .replace(/\bJ\.p\. morgan\b/g, "J.P. Morgan")
        .replace(/\bJpmorgan\b/g, "JPMorgan")
        .replace(/\bMcdonalds\b/g, "McDonald's")
        .replace(/\bWalmart\b/g, "Walmart")
        .replace(/\bTarget\b/g, "Target")
        .replace(/\bCvs\b/g, "CVS")
        .replace(/\bUps\b/g, "UPS")
        .replace(/\bFedex\b/g, "FedEx")
        .replace(/\bUsps\b/g, "USPS")
        .replace(/\bUs bank\b/g, "US Bank")
        .replace(/\bWells fargo\b/g, "Wells Fargo")
        .replace(/\bBank of america\b/g, "Bank of America")
        .replace(/\bJpmorgan chase\b/g, "JPMorgan Chase")
        .replace(/\bCitibank\b/g, "Citibank")
        .replace(/\bCapital one\b/g, "Capital One");
    // Handle special cases with ampersands
    cleaned = cleaned.replace(/\bAnd\b/g, "&");
    // Trim again and return
    return cleaned.trim() || "Unknown";
}
// Cache for city names to avoid repeated database queries
let cityNamesCache = [];
let stateCodesCache = [];
/**
 * Initialize the city and state caches from the database
 * This should be called before using cleanLocation in bulk operations
 */
export async function initLocationCache() {
    try {
        // Get all city names from the database
        const cities = await prisma.city.findMany({
            select: { name: true },
        });
        cityNamesCache = cities.map((city) => city.name.toLowerCase());
        // Get all state codes from the database
        const states = await prisma.state.findMany({
            select: { code: true },
        });
        stateCodesCache = states
            .filter((state) => state.code) // Filter out null values
            .map((state) => state.code.toUpperCase());
        console.log(`Loaded ${cityNamesCache.length} cities and ${stateCodesCache.length} states into location cache`);
    }
    catch (error) {
        console.error("Failed to initialize location cache:", error);
        // Initialize with empty arrays if there's an error
        cityNamesCache = [];
        stateCodesCache = [];
    }
}
/**
 * Clean location string and match with cities in the database
 * @param location Raw location string
 * @returns Cleaned location string
 */
export async function cleanLocationAsync(location) {
    if (!location)
        return "";
    // Ensure cache is initialized
    if (cityNamesCache.length === 0) {
        await initLocationCache();
    }
    // Basic cleaning
    let cleaned = basicLocationCleaning(location);
    // Try to match with a city in our database
    const cleanedLower = cleaned.toLowerCase();
    // Check if the location contains a known city name
    for (const cityName of cityNamesCache) {
        if (cleanedLower.includes(cityName)) {
            // Get the original capitalization from the database
            const city = await prisma.city.findFirst({
                where: { name: { equals: cityName, mode: "insensitive" } },
            });
            if (city) {
                // Replace the city name with the properly capitalized version from the database
                const cityRegex = new RegExp(`\\b${cityName}\\b`, "i");
                cleaned = cleaned.replace(cityRegex, city.name);
            }
        }
    }
    // Check for state codes and ensure they're uppercase
    for (const stateCode of stateCodesCache) {
        const stateRegex = new RegExp(`\\b${stateCode}\\b`, "i");
        if (stateRegex.test(cleaned)) {
            cleaned = cleaned.replace(stateRegex, stateCode);
        }
    }
    return cleaned.trim();
}
/**
 * Clean location string
 * @param location Raw location string
 * @returns Cleaned location string
 */
export function cleanLocation(location) {
    if (!location)
        return "";
    // Handle specific edge cases first
    if (location ===
        "Southern California, our hospital is renowned for its exceptional care and supportive team environment") {
        return "CA";
    }
    if (location === "SWAT Administrator for California") {
        return "CA";
    }
    // Handle "Retirement benefits, LI" and similar cases
    if (location === "Retirement benefits, LI") {
        // We'll handle this in JobService.cleanJobData based on company name
        return "Unknown";
    }
    // Handle "company" and "unknown" locations
    if (location.toLowerCase() === "company") {
        // We'll handle this in JobService.cleanJobData based on company name
        return "Unknown";
    }
    if (location.toLowerCase() === "unknown") {
        // We'll handle this in JobService.cleanJobData based on company name
        return "Unknown";
    }
    // Check for benefits or job features ending with ", LI"
    if (/^(Benefits|Retirement|Insurance|Health|Dental|Vision|401k|PTO).*,\s*LI$/i.test(location)) {
        // For LinkedIn indicators, we'll return a default location
        // In a real implementation, we would use the company name to determine the location
        // For now, we'll just return "Unknown" as a fallback
        return "Unknown";
    }
    // Handle newlines first
    if (location.includes("\n") || location.includes("\r")) {
        // Extract the first line only
        const firstLine = location.split(/[\n\r]/)[0].trim();
        return cleanLocation(firstLine); // Process the first line
    }
    // Check for multiple spaces (like "Ledgent          Calabasas, CA")
    if (location.includes("          ") || location.includes("  ")) {
        const parts = location.split(/\s{2,}/);
        if (parts.length > 1) {
            // The actual location is usually the last part
            const lastPart = parts[parts.length - 1].trim();
            // Check if it's a valid location
            if (lastPart.includes(", ")) {
                // If the last part contains a company name followed by a location, extract just the location
                if (lastPart.includes("Ledgent")) {
                    const cityMatch = lastPart.match(/([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
                    if (cityMatch) {
                        return `${cityMatch[1]}, ${cityMatch[2].toUpperCase()}`;
                    }
                }
                return lastPart;
            }
        }
    }
    // Check for company name followed by location (like "Global Agoura Hills, CA")
    const companyLocationMatch = location.match(/^(Global|United|American|National|International|Worldwide)\s+([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
    if (companyLocationMatch) {
        // Extract just the location part
        return `${companyLocationMatch[2]}, ${companyLocationMatch[3].toUpperCase()}`;
    }
    // Check for hybrid format (Hybrid - City, State)
    if (location.toLowerCase().startsWith("hybrid - ")) {
        const locationPart = location.substring("hybrid - ".length).trim();
        return cleanLocation(locationPart); // Process the location part
    }
    // Check for remote indicators
    const remoteIndicators = [
        "remote",
        "work from home",
        "wfh",
        "virtual",
        "anywhere",
        "nationwide",
        "telecommute",
        "work remotely",
        "remote eligible",
        "hybrid remote",
        "in person/remote",
    ];
    if (remoteIndicators.some((indicator) => location.toLowerCase().includes(indicator))) {
        // Try to extract city from the text
        const cityMatch = location.match(/([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
        if (cityMatch) {
            return `${cityMatch[1]}, ${cityMatch[2].toUpperCase()}`;
        }
        // For remote locations, use "Remote" as the location
        // This ensures we don't have blank locations in the UI
        return "Remote"; // Use "Remote" for remote locations
    }
    // Check for extremely long locations (likely job descriptions)
    if (location.length > 50) {
        // Check for time indicators like "days ago"
        const timeMatch = location.match(/(\d+\s+days?\s+ago|\d+\s+hours?\s+ago|\d+\s+minutes?\s+ago)/i);
        if (timeMatch) {
            // Split by the time indicator and check the part after it
            const parts = location.split(timeMatch[0]);
            if (parts.length > 1 && parts[1].includes(", ")) {
                // Extract the city and state after the time indicator
                const afterTimePart = parts[1].trim();
                const cityStateMatch = afterTimePart.match(/([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
                if (cityStateMatch) {
                    return `${cityStateMatch[1]}, ${cityStateMatch[2].toUpperCase()}`;
                }
            }
        }
        // Check for job title indicators
        const jobTitleMatch = location.match(/(Job Title:|About the Role:|This is a|Position:|Role:|Opportunity:)/i);
        if (jobTitleMatch) {
            // Try to extract city before the job title
            const beforeJobTitle = location.substring(0, jobTitleMatch.index).trim();
            const cityStateMatch = beforeJobTitle.match(/([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
            if (cityStateMatch) {
                return `${cityStateMatch[1]}, ${cityStateMatch[2].toUpperCase()}`;
            }
            // If no city found before job title, check if there's a standalone city name
            const cityMatch = beforeJobTitle.match(/\b(Thousand Oaks|Agoura Hills|Los Angeles|San Diego|San Francisco|Sacramento|San Jose)\b/i);
            if (cityMatch) {
                return `${cityMatch[1]}, CA`;
            }
            // If still no city found, return the state if it's mentioned
            if (beforeJobTitle.toLowerCase().includes("california")) {
                return "CA";
            }
            return "Unknown"; // No valid location found
        }
        // Try to extract city from the text
        const cityMatch = location.match(/([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
        if (cityMatch) {
            return `${cityMatch[1]}, ${cityMatch[2].toUpperCase()}`;
        }
        // Check for standalone city names
        const standaloneCityMatch = location.match(/\b(Thousand Oaks|Agoura Hills|Los Angeles|San Diego|San Francisco|Sacramento|San Jose)\b/i);
        if (standaloneCityMatch) {
            return `${standaloneCityMatch[1]}, CA`;
        }
        // If location contains "Southern California" or similar
        const southernCalMatch = location.match(/\b(Southern California)\b/i);
        if (southernCalMatch) {
            return "Southern California";
        }
        // Check for "Southern California," followed by text
        const southernCalCommaMatch = location.match(/\b(Southern California),/i);
        if (southernCalCommaMatch) {
            return "Southern California";
        }
        // Check for "California" in the text
        if (location.toLowerCase().includes("california")) {
            return "CA";
        }
    }
    // Check for HTML or JavaScript in location
    if (location.includes("<") ||
        location.includes(">") ||
        location.includes("function") ||
        location.includes("window.") ||
        location.includes("document.") ||
        location.includes("var ") ||
        location.includes("const ") ||
        location.includes("let ") ||
        location.includes("script")) {
        return "Unknown"; // Use "Unknown" for invalid locations
    }
    // Check for non-location text
    const nonLocationIndicators = [
        "be an early applicant",
        "additional verification required",
        "easy apply",
        "apply now",
        "click to apply",
        "verification required",
        "posted",
        "new job",
        "active job",
        "full time",
        "part time",
        "contract",
        "permanent",
        "temporary",
        "freelance",
        "internship",
        "service temporarily unavailable",
        "error",
        "job overview",
        "about the role",
        "about the job",
        "about the company",
        "about us",
        "days ago",
        "hours ago",
        "minutes ago",
        "seconds ago",
        "week ago",
        "month ago",
        "year ago",
        "this", // Single word "this"
        "enable", // Single word "enable"
        "ad", // Single word "ad"
        "current", // Single word "current"
        "global", // Company name "Global"
        "southern california", // Too generic
        "job title", // Job title prefix
        "administrator", // Job title
        "clinical", // Job title
        "pool", // Job title
        "float", // Job title
        "opportunity", // Job description
        "renowned", // Job description
        "exceptional", // Job description
        "supportive", // Job description
        "team", // Job description
        "hospital", // Job description
        "care", // Job description
        "benefits", // Job benefits
        "retirement", // Job benefits
        "insurance", // Job benefits
        "health", // Job benefits
        "dental", // Job benefits
        "vision", // Job benefits
        "401k", // Job benefits
        "pto", // Job benefits
        ", li", // LinkedIn indicator
        "li,", // LinkedIn indicator
    ];
    if (nonLocationIndicators.some((indicator) => location.toLowerCase().includes(indicator))) {
        // Try to extract city from the text
        const cityMatch = location.match(/([A-Z][a-zA-Z\s]+),\s*([A-Z]{2})/i);
        if (cityMatch) {
            return `${cityMatch[1]}, ${cityMatch[2].toUpperCase()}`;
        }
        return "Unknown"; // Use "Unknown" for invalid locations
    }
    // Basic cleaning
    let cleaned = basicLocationCleaning(location);
    // Fix common typos in city names
    cleaned = cleaned
        .replace(/Los Ngeles/i, "Los Angeles")
        .replace(/San Diegoo/i, "San Diego")
        .replace(/San Dieg\b/i, "San Diego")
        .replace(/El Caj N/i, "El Cajon");
    // Special case for "San Diego CA" without comma
    if (cleaned.match(/San Diego CA$/i)) {
        return "San Diego CA";
    }
    // Ensure state codes are uppercase
    const stateCodeMatch = cleaned.match(/,\s*([a-z]{2})$/i);
    if (stateCodeMatch) {
        cleaned = cleaned.replace(/,\s*[a-z]{2}$/i, `, ${stateCodeMatch[1].toUpperCase()}`);
    }
    // Special case for "California, CA"
    if (cleaned === "California, CA") {
        return "CA";
    }
    // We now use the database for city matching, but keep this function
    // for backward compatibility
    return cleaned.trim();
}
/**
 * Basic location string cleaning that's common to both sync and async versions
 * @param location Raw location string
 * @returns Partially cleaned location string
 */
function basicLocationCleaning(location) {
    // Trim and standardize whitespace
    let cleaned = location.trim().replace(/\s+/g, " ");
    // Remove HTML entities and convert to plain text
    cleaned = cleaned
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
    // Remove special characters except for common punctuation
    cleaned = cleaned.replace(/[^a-zA-Z0-9\s\-&.,()]/g, " ").replace(/\s+/g, " ");
    // Remove newlines and tabs
    cleaned = cleaned.replace(/[\n\r\t]+/g, " ").replace(/\s+/g, " ");
    // Remove common prefixes
    const prefixes = [
        /^remote\s+in\s+/i,
        /^location:?\s*/i,
        /^based\s+in\s+/i,
        /^work\s+from\s+/i,
        /^anywhere\s+in\s+/i,
        /^in\s+/i,
        /^at\s+/i,
        /^job\s+location:?\s*/i,
        /^position\s+location:?\s*/i,
        /^working\s+location:?\s*/i,
        /^site\s+location:?\s*/i,
        /^office\s+location:?\s*/i,
    ];
    for (const prefix of prefixes) {
        cleaned = cleaned.replace(prefix, "");
    }
    // Remove common suffixes
    const suffixes = [
        /\s+area$/i,
        /\s+region$/i,
        /\s+and surrounding areas$/i,
        /\s+and vicinity$/i,
        /\s+metropolitan area$/i,
        /\s+metro$/i,
    ];
    for (const suffix of suffixes) {
        cleaned = cleaned.replace(suffix, "");
    }
    // Save state code if present
    let stateCode = null;
    const stateCodeMatch = cleaned.match(/,\s*([A-Za-z]{2})$/);
    if (stateCodeMatch) {
        stateCode = stateCodeMatch[1].toUpperCase();
    }
    // Convert to lowercase first to ensure consistent capitalization
    cleaned = cleaned.toLowerCase();
    // Capitalize first letter of each word except for certain prepositions and articles
    cleaned = cleaned.replace(/\b\w+\b/g, (word) => {
        const lowerCaseWords = [
            "a",
            "an",
            "the",
            "and",
            "but",
            "or",
            "for",
            "nor",
            "on",
            "at",
            "to",
            "from",
            "by",
            "with",
            "in",
            "of",
        ];
        return lowerCaseWords.includes(word) && !cleaned.startsWith(word)
            ? word
            : word.charAt(0).toUpperCase() + word.slice(1);
    });
    // Restore state code if it was present
    if (stateCode) {
        cleaned = cleaned.replace(/,\s*[a-z]{2}$/, `, ${stateCode}`);
    }
    return cleaned;
}
