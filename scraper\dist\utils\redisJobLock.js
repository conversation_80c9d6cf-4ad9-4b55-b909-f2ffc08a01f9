// cron/utils/redisJobLock.ts
// Distributed locking mechanism to prevent duplicate job execution using Redis
import { logger } from "./logger";
import Redis from "ioredis";
import { config } from "../config";
// Create a Redis client for locking with fallback for local development
const redisUrl = config.redis?.url ?? "redis://localhost:6379";
let redis = null;
let redisAvailable = false;
// Try to create Redis client with error handling
try {
    redis = new Redis(redisUrl, {
        connectTimeout: 5000, // 5 second timeout
        retryStrategy: () => null, // Don't retry on connection failure
        enableOfflineQueue: false, // Don't queue commands when offline
    });
    // Test the connection
    redis
        .ping()
        .then(() => {
        redisAvailable = true;
        logger.info("✅ Redis connection established for job locking");
    })
        .catch((error) => {
        logger.warn(`⚠️ Redis not available for job locking: ${error.message}`);
        logger.warn("🔄 Falling back to in-memory locking (single instance only)");
        redisAvailable = false;
        redis?.disconnect();
        redis = null;
    });
    redis.on("error", (error) => {
        logger.warn(`⚠️ Redis connection error: ${error.message}`);
        redisAvailable = false;
    });
}
catch (error) {
    logger.warn(`⚠️ Failed to create Redis client: ${error}`);
    logger.warn("🔄 Falling back to in-memory locking (single instance only)");
    redisAvailable = false;
    redis = null;
}
// In-memory lock storage for fallback
const inMemoryLocks = new Map();
/**
 * Acquire a job lock to prevent duplicate job execution
 * @param options Lock options
 * @returns Lock result
 */
export async function acquireJobLock(options) {
    const { jobType, timeoutSeconds = 3600, // 1 hour default
    wait = false, maxWaitMs = 30000, // 30 seconds default
    retryIntervalMs = 1000, // 1 second default
     } = options;
    const lockKey = `job_lock:${jobType}`;
    const lockId = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    // Function to release the lock
    const releaseLock = async () => {
        try {
            // Use Lua script to ensure we only delete our own lock
            const script = `
        if redis.call("GET", KEYS[1]) == ARGV[1] then
          return redis.call("DEL", KEYS[1])
        else
          return 0
        end
      `;
            const result = await redis.eval(script, 1, lockKey, lockId);
            if (result === 1) {
                logger.info(`🔓 Released job lock for ${jobType} (${lockId})`);
                return true;
            }
            else {
                logger.warn(`⚠️ Lock for ${jobType} was already released or owned by another process`);
                return false;
            }
        }
        catch (error) {
            logger.error(`❌ Failed to release job lock for ${jobType} (${lockId}):`, error);
            return false;
        }
    };
    // Failed lock result
    const failedResult = {
        acquired: false,
        error: `Could not acquire job lock for ${jobType}`,
        release: async () => true, // No-op for failed acquisition
    };
    try {
        // Try to acquire the lock
        const acquireLock = async () => {
            try {
                // Use Redis if available, otherwise use in-memory fallback
                if (redisAvailable && redis) {
                    // Try to set the lock with NX (only if it doesn't exist) and EX (expiration)
                    const result = await redis.set(lockKey, lockId, "EX", timeoutSeconds, "NX");
                    if (result === "OK") {
                        logger.info(`🔒 Acquired Redis job lock for ${jobType} (${lockId})`);
                        return true;
                    }
                    else {
                        // Lock already exists
                        try {
                            // Get the TTL to log when it expires
                            const ttl = await redis.ttl(lockKey);
                            logger.warn(`⚠️ Redis job lock already exists for ${jobType} (expires in ${ttl} seconds)`);
                        }
                        catch (ttlError) {
                            logger.warn(`⚠️ Redis job lock already exists for ${jobType}. Failed to get TTL:`, ttlError);
                        }
                        return false;
                    }
                }
                else {
                    // Use in-memory fallback
                    const now = Date.now();
                    const expiresAt = now + timeoutSeconds * 1000;
                    // Clean up expired locks
                    for (const [key, lock] of inMemoryLocks.entries()) {
                        if (lock.expiresAt <= now) {
                            inMemoryLocks.delete(key);
                        }
                    }
                    // Check if lock already exists
                    if (inMemoryLocks.has(lockKey)) {
                        const existingLock = inMemoryLocks.get(lockKey);
                        const ttlSeconds = Math.max(0, Math.floor((existingLock.expiresAt - now) / 1000));
                        logger.warn(`⚠️ In-memory job lock already exists for ${jobType} (expires in ${ttlSeconds} seconds)`);
                        return false;
                    }
                    // Acquire the lock
                    inMemoryLocks.set(lockKey, { lockId, expiresAt });
                    logger.info(`🔒 Acquired in-memory job lock for ${jobType} (${lockId})`);
                    return true;
                }
            }
            catch (error) {
                logger.error(`❌ Error trying to acquire lock for ${jobType}:`, error);
                throw error;
            }
        };
        // Try to acquire the lock with retry if wait is enabled
        if (wait) {
            const startTime = Date.now();
            while (true) {
                const acquired = await acquireLock();
                if (acquired) {
                    return {
                        acquired: true,
                        lockId,
                        release: releaseLock,
                    };
                }
                // Check if we've waited too long
                if (Date.now() - startTime >= maxWaitMs) {
                    logger.warn(`⏱️ Timed out waiting for job lock for ${jobType}`);
                    return failedResult;
                }
                // Wait before retrying
                logger.info(`⏳ Waiting for job lock for ${jobType} (${retryIntervalMs}ms)`);
                await new Promise((resolve) => setTimeout(resolve, retryIntervalMs));
            }
        }
        else {
            // Just try once
            const acquired = await acquireLock();
            if (acquired) {
                return {
                    acquired: true,
                    lockId,
                    release: releaseLock,
                };
            }
            return failedResult;
        }
    }
    catch (error) {
        logger.error(`❌ Error acquiring job lock for ${jobType}:`, error);
        return {
            acquired: false,
            error: `Error acquiring job lock: ${error.message ?? String(error)}`,
            release: async () => true, // No-op for failed acquisition
        };
    }
}
/**
 * Execute a job with a lock to prevent duplicate execution
 * @param jobType Job type identifier
 * @param timeoutSeconds Lock timeout in seconds
 * @param job Job function to execute
 * @returns Result of the job function
 */
export async function withJobLock(jobType, timeoutSeconds, job) {
    const lock = await acquireJobLock({
        jobType,
        timeoutSeconds,
    });
    if (!lock.acquired) {
        logger.warn(`⚠️ Could not acquire job lock for ${jobType}, skipping execution`);
        return null;
    }
    try {
        logger.info(`🚀 Executing job ${jobType} with lock`);
        return await job();
    }
    finally {
        await lock.release();
    }
}
// Handle process exit to clean up Redis connection
process.on("exit", () => {
    if (redis && redis.status === "ready") {
        redis.quit();
    }
});
export default withJobLock;
