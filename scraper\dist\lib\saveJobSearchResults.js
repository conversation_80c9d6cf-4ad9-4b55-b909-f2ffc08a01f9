import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
const prisma = new PrismaClient();
export async function saveJobSearchResults({ scoredJobs, userId, profileId, }) {
    const entries = scoredJobs.map((job) => ({
        where: {
            userId_jobId_profileId: {
                userId,
                jobId: job.id,
                profileId,
            },
        },
        update: {
            matchScore: job.matchScore,
        },
        create: {
            userId,
            jobId: job.id,
            profileId,
            matchScore: job.matchScore,
        },
    }));
    const results = await Promise.allSettled(entries.map((entry) => prisma.jobMatchResult.upsert(entry)));
    const failed = results.filter((r) => r.status === "rejected");
    if (failed.length > 0) {
        failed.forEach((f, i) => {
            logger.warn(`❌ Failed to upsert match ${i}: ${f.reason}`);
        });
    }
    else {
        logger.info(`✅ Saved ${entries.length} matched jobs`);
    }
}
