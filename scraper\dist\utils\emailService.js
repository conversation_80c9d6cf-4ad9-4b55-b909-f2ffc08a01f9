import { Resend } from "resend";
import { logger } from "./logger.js";
import { renderEmailTemplate, EmailTemplate } from "../email/renderer.js";
import { v4 as uuidv4 } from "uuid";
// Load environment variables
import dotenv from "dotenv";
dotenv.config();
// Initialize Resend with API key from environment variables
const resendApiKey = process.env.RESEND_API_KEY;
if (!resendApiKey) {
    logger.error("❌ RESEND_API_KEY environment variable is not set");
}
const resend = new Resend(resendApiKey);
// Default sender and audience ID
const resendDomain = process.env.RESEND_DOMAIN || "hirli.co";
const DEFAULT_FROM_EMAIL = `notifications@${resendDomain}`;
// Hardcode the developer audience ID to ensure it's always available
const DEFAULT_AUDIENCE_ID = process.env.DEVELOPER_AUDIENCE || "76c9f8c3-1524-48e3-b9f6-4b5a3a522e82";
// Export EmailTemplate for external use
export { EmailTemplate };
/**
 * Email notification types
 */
export var EmailNotificationType;
(function (EmailNotificationType) {
    EmailNotificationType["JOB_SUMMARY"] = "job-summary";
    EmailNotificationType["ERROR_ALERT"] = "error-alert";
    EmailNotificationType["USAGE_REPORT"] = "usage-report";
    EmailNotificationType["CRON_ERROR"] = "CRON_ERROR";
    EmailNotificationType["MEMORY_LIMIT"] = "memory-limit";
    EmailNotificationType["PROXY_DOWN"] = "proxy-down";
    EmailNotificationType["SYSTEM_OVERLOAD"] = "system-overload";
    EmailNotificationType["SYSTEM_HEALTH"] = "system-alert";
})(EmailNotificationType || (EmailNotificationType = {}));
/**
 * Sleep for a specified number of milliseconds
 * @param ms Milliseconds to sleep
 * @returns Promise that resolves after the specified time
 */
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
/**
 * Send an email notification to admin
 *
 * @param type Type of notification
 * @param data Data for the email template
 * @returns Promise with the email ID
 */
export async function sendEmailNotification(type, data) {
    try {
        // Determine which email template to use
        let emailTemplate;
        const baseUrl = process.env.PUBLIC_BASE_URL || "https://hirli.co";
        const systemStatusUrl = `${baseUrl}/system-status`;
        let templateData = {
            ...data,
            baseUrl,
            systemStatusUrl,
            appUrl: baseUrl,
        };
        let emailContent = { subject: "" };
        switch (type) {
            case EmailNotificationType.JOB_SUMMARY:
                emailTemplate = EmailTemplate.JOB_SUMMARY;
                emailContent = getEmailContent(type, data);
                templateData = {
                    ...data,
                    appName: process.env.APP_NAME || "Hirli",
                    subject: emailContent.subject,
                };
                break;
            case EmailNotificationType.ERROR_ALERT:
                emailTemplate = EmailTemplate.ERROR_REPORT;
                emailContent = getEmailContent(type, data);
                templateData = {
                    ...data,
                    appName: process.env.APP_NAME || "Hirli",
                    subject: emailContent.subject,
                };
                break;
            case EmailNotificationType.CRON_ERROR:
                emailTemplate = EmailTemplate.ERROR_REPORT;
                emailContent = getEmailContent(type, data);
                templateData = {
                    ...data,
                    appName: process.env.APP_NAME || "Hirli",
                    subject: emailContent.subject,
                };
                break;
            case EmailNotificationType.USAGE_REPORT:
                emailTemplate = EmailTemplate.USAGE_REPORT;
                emailContent = getEmailContent(type, data);
                templateData = {
                    ...data,
                    appName: process.env.APP_NAME || "Hirli",
                    subject: emailContent.subject,
                };
                break;
            default:
                emailTemplate = EmailTemplate.SYSTEM_ALERT;
                emailContent = {
                    subject: `Notification from ${process.env.APP_NAME || "Hirli"}`,
                };
                break;
        }
        // Render the email template
        const renderedTemplate = await renderEmailTemplate(emailTemplate, templateData);
        if (!renderedTemplate) {
            logger.error(`❌ Failed to render template: ${emailTemplate}`);
            return null;
        }
        // Use provided subject or the one from the template
        const subject = emailContent.subject || renderedTemplate.subject;
        const html = renderedTemplate.html;
        const text = renderedTemplate.text;
        // Generate a unique ID for this email
        const emailId = `email_${Date.now()}_${uuidv4().substring(0, 8)}`;
        // Always send to the developer audience for all cron job emails
        // We've hardcoded the developer audience ID, so this should always be available
        logger.info(`📧 Sending to developer audience: ${DEFAULT_AUDIENCE_ID}`);
        const audienceResult = await sendToAudience(DEFAULT_AUDIENCE_ID, subject, html, DEFAULT_FROM_EMAIL);
        if (audienceResult) {
            logger.info(`✅ Successfully sent to developer audience: ${DEFAULT_AUDIENCE_ID}`);
            return emailId;
        }
        else {
            logger.warn(`⚠️ Failed to send to developer audience, falling back to direct email`);
        }
        // Fall back to direct email if audience sending fails or no audience is configured
        const emailOptions = {
            from: DEFAULT_FROM_EMAIL,
            to: process.env.ADMIN_EMAIL || "<EMAIL>",
            subject,
            html,
            text,
            tags: [{ name: "type", value: type }],
        };
        logger.info(`📧 Sending email notification: ${subject}`);
        // Send the email
        const { data: emailData, error } = await resend.emails.send(emailOptions);
        if (error) {
            logger.error(`❌ Failed to send email: ${JSON.stringify(error)}`);
            return null;
        }
        logger.info(`✅ Email notification sent with ID: ${emailData?.id || emailId}`);
        return emailData?.id || emailId;
    }
    catch (error) {
        logger.error(`❌ Failed to send ${type} email notification:`, error);
        return null;
    }
}
/**
 * Get email content based on notification type
 */
function getEmailContent(type, data) {
    const appName = process.env.APP_NAME || "Hirli";
    switch (type) {
        case EmailNotificationType.JOB_SUMMARY:
            return {
                subject: `${data.reportTitle || "Job Processing Summary"} - ${data.reportDate || new Date().toLocaleDateString()}`,
            };
        case EmailNotificationType.ERROR_ALERT:
            return {
                subject: `⚠️ ${appName} Alert: ${data.reportTitle || "System Alert"}`,
            };
        case EmailNotificationType.CRON_ERROR:
            return {
                subject: `❌ ${appName} Error: ${data.errorTitle || "System Error"}`,
            };
        case EmailNotificationType.USAGE_REPORT:
            return {
                subject: `📊 ${appName} Usage Report: ${data.reportDate || new Date().toLocaleDateString()}`,
            };
        case EmailNotificationType.SYSTEM_HEALTH:
            return {
                subject: `🔔 ${appName} System Health Alert: ${data.issues ? data.issues.join(", ") : "Health Check"}`,
            };
        case EmailNotificationType.SYSTEM_OVERLOAD:
            return {
                subject: `⚠️ ${appName} System Overload Alert: Resource Constraints Detected`,
            };
        case EmailNotificationType.MEMORY_LIMIT:
            return {
                subject: `⚠️ ${appName} Memory Alert: High Memory Usage Detected`,
            };
        case EmailNotificationType.PROXY_DOWN:
            return {
                subject: `🔴 ${appName} Connectivity Alert: Proxy Service Unavailable`,
            };
        default:
            return {
                subject: `Notification from ${appName}`,
            };
    }
}
/**
 * Send an email using a template
 * @param template The email template to use
 * @param data Template data
 * @param options Additional options for sending the email
 * @returns Promise with the result of sending the email
 */
export async function sendTemplatedEmail(template, data = {}, options = {}) {
    try {
        // Render the template using the local renderer
        const renderedTemplate = await renderEmailTemplate(template, data);
        if (!renderedTemplate) {
            logger.error(`❌ Failed to render template: ${template}`);
            return false;
        }
        // Use provided subject or the one from the template
        const subject = options.subject || renderedTemplate.subject;
        const html = renderedTemplate.html;
        // If audience ID is provided, send to audience
        // For cron jobs, always prefer the developer audience
        if (options.audienceId || DEFAULT_AUDIENCE_ID) {
            const audienceId = options.audienceId || DEFAULT_AUDIENCE_ID;
            logger.info(`📧 Sending templated email to audience: ${audienceId}`);
            return await sendToAudience(audienceId, subject, html, options.from || DEFAULT_FROM_EMAIL);
        }
        // If recipient(s) provided, send directly
        if (options.to) {
            return await sendDirectEmail(options.to, subject, html, options.from || DEFAULT_FROM_EMAIL);
        }
        logger.error(`❌ No recipient (to) or audience specified for email: ${template}`);
        return false;
    }
    catch (error) {
        logger.error(`❌ Error sending templated email: ${error}`);
        return false;
    }
}
/**
 * Send an email to an audience
 * @param audienceId The audience ID
 * @param subject Email subject
 * @param html Email HTML content
 * @param from Sender email
 * @returns Promise with the result of sending the email
 */
async function sendToAudience(audienceId, subject, html, from) {
    try {
        logger.info(`📧 Sending email to audience: ${audienceId}`);
        // Create the broadcast content
        const broadcastContent = {
            audienceId,
            from,
            subject,
            html,
        };
        // Step 1: Create the broadcast
        const { data: broadcastData, error: createError } = await resend.broadcasts.create(broadcastContent);
        if (createError) {
            logger.error(`❌ Failed to create broadcast: ${JSON.stringify(createError)}`);
            return false;
        }
        logger.info(`✅ Successfully created broadcast: ${broadcastData?.id}`);
        // Add a delay to avoid rate limiting (Resend limits to 2 requests per second)
        await sleep(600); // Wait 600ms between API calls
        // Step 2: Send the broadcast
        if (broadcastData?.id) {
            // Send the broadcast immediately
            const { error: sendError } = await resend.broadcasts.send(broadcastData.id);
            if (sendError) {
                logger.error(`❌ Failed to send broadcast: ${JSON.stringify(sendError)}`);
                return false;
            }
            logger.info(`✅ Successfully sent broadcast: ${broadcastData.id}`);
            return true;
        }
        else {
            logger.error(`❌ No broadcast ID returned from create operation`);
            return false;
        }
    }
    catch (error) {
        logger.error(`❌ Error sending to audience: ${error}`);
        return false;
    }
}
/**
 * Send a direct email to recipient(s)
 * @param to Recipient(s)
 * @param subject Email subject
 * @param html Email HTML content
 * @param from Sender email
 * @returns Promise with the result of sending the email
 */
async function sendDirectEmail(to, subject, html, from) {
    try {
        logger.info(`📧 Sending direct email to: ${Array.isArray(to) ? to.join(", ") : to}`);
        // Add a delay to avoid rate limiting (Resend limits to 2 requests per second)
        await sleep(600); // Wait 600ms between API calls
        const { data, error } = await resend.emails.send({
            from,
            to,
            subject,
            html,
        });
        if (error) {
            logger.error(`❌ Failed to send direct email: ${JSON.stringify(error)}`);
            return false;
        }
        logger.info(`✅ Successfully sent direct email: ${data?.id}`);
        return true;
    }
    catch (error) {
        logger.error(`❌ Error sending direct email: ${error}`);
        return false;
    }
}
