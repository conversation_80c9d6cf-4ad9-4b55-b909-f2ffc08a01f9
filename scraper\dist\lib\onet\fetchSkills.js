// cron/lib/linkedin/fetchSkills.ts
import { PrismaClient } from "@prisma/client";
import { logger } from "../../utils/logger";
import fs from "fs/promises";
import path from "path";
import readline from "readline";
const prisma = new PrismaClient();
const DATA_PATH = path.resolve("data/Technology%20Skills.txt");
// Helper – treat blank or literal "N/A" (any case) as missing
const isNA = (v) => !v || v.trim() === "" || v.trim().toLowerCase() === "n/a";
export async function fetchOnetSkills() {
    logger.info("📥 Reading O*NET technology skills file…");
    const fh = await fs.open(DATA_PATH, "r");
    const rl = readline.createInterface({
        input: fh.createReadStream(),
        crlfDelay: Infinity,
    });
    const seen = new Set(); // avoid duplicates in the same run
    let count = 0;
    let header = true;
    for await (const raw of rl) {
        if (header) {
            header = false; // skip header row
            continue;
        }
        const [socCode, // e.g. "11-1011.00"
        toolName, // e.g. "Python"
        commodityCode, // not used right now
        commodityTitle, // e.g. "Programming languages"
        isHotTech, // "Y" | "N"
        isInDemand, // "Y" | "N"
        ] = raw.split("\t");
        if (isNA(toolName))
            continue; // must have a valid tool name
        const skillName = toolName.trim();
        if (seen.has(skillName))
            continue; // duplicate within the same file
        // Skip if the skill already exists in DB
        const exists = await prisma.skills.findUnique({
            where: { name: skillName },
            select: { id: true },
        });
        if (exists)
            continue;
        // Insert new skill
        await prisma.skills.create({
            data: {
                name: skillName,
                type: commodityTitle?.trim() || null,
                source: "O*NET",
                // You can store isHotTech / isInDemand if you add columns for them
            },
        });
        seen.add(skillName);
        count++;
        logger.debug(`🛠 Added skill: ${skillName} (${commodityTitle})`);
    }
    await fh.close();
    logger.info(`✅ Stored ${count} new O*NET technology skills in DB`);
}
