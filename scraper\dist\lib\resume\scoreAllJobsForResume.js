import { PrismaClient } from "@prisma/client";
import { scoreJobAgainstResume } from "./scoreJobAgainstResume";
const prisma = new PrismaClient();
export async function scoreAllJobsForResume(resumeText) {
    const jobs = await prisma.jobListing.findMany({
        where: { isActive: true, description: { not: null } },
        take: 500, // Can tune or paginate
    });
    const scored = jobs.map((job) => ({
        ...job,
        matchScore: scoreJobAgainstResume(resumeText, {
            title: job.title,
            company: job.company,
            description: job.description,
        }),
    }));
    // Sort from highest to lowest match
    scored.sort((a, b) => b.matchScore - a.matchScore);
    return scored;
}
