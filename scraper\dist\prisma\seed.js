import { PrismaClient } from '@prisma/client';
import { FEATURES } from '../src/lib/models/features';
// This is needed because FEATURE_LIMITS is removed from the import
const FEATURE_LIMITS = {};
const prisma = new PrismaClient();
async function main() {
    console.log('Seeding database...');
    // Seed features
    console.log('Seeding features...');
    for (const feature of FEATURES) {
        const existingFeature = await prisma.feature.findUnique({
            where: { id: feature.id },
        });
        if (!existingFeature) {
            await prisma.feature.create({
                data: {
                    id: feature.id,
                    name: feature.name,
                    description: feature.description || '',
                    category: feature.category || 'general',
                    icon: feature.icon || null,
                    beta: feature.beta || false,
                    updatedAt: new Date(),
                },
            });
            console.log(`Created feature: ${feature.name}`);
        }
        else {
            console.log(`Feature already exists: ${feature.name}`);
        }
        // Seed feature limits
        if (feature.limits) {
            for (const limit of feature.limits) {
                if (!limit || !limit.id)
                    continue;
                const existingLimit = await prisma.featureLimit.findUnique({
                    where: { id: limit.id },
                });
                if (!existingLimit) {
                    await prisma.featureLimit.create({
                        data: {
                            id: limit.id,
                            featureId: feature.id,
                            name: limit.name,
                            description: limit.description || '',
                            defaultValue: (limit.defaultValue || 10).toString(),
                            type: limit.type,
                            unit: limit.unit || null,
                            resetDay: limit.resetDay || null,
                        },
                    });
                    console.log(`Created feature limit: ${limit.name}`);
                }
                else {
                    console.log(`Feature limit already exists: ${limit.name}`);
                }
            }
        }
    }
    // Seed default plans
    console.log('Seeding default plans...');
    // Define default plans
    const defaultPlans = [
        {
            id: 'free',
            name: 'Free',
            description: 'Basic features for personal use',
            section: 'pro',
            monthlyPrice: 0,
            annualPrice: 0,
            popular: false,
            features: [
                { featureId: 'dashboard', accessLevel: 'included' },
                { featureId: 'profile', accessLevel: 'included' },
                {
                    featureId: 'resume_scanner',
                    accessLevel: 'limited',
                    limits: [{ limitId: 'resume_scans_per_month', value: '10' }],
                },
            ],
        },
        {
            id: 'casual',
            name: 'Casual',
            description: 'For occasional job seekers',
            section: 'pro',
            monthlyPrice: 999,
            annualPrice: 9990,
            popular: false,
            features: [
                { featureId: 'dashboard', accessLevel: 'included' },
                { featureId: 'profile', accessLevel: 'included' },
                {
                    featureId: 'resume_scanner',
                    accessLevel: 'limited',
                    limits: [{ limitId: 'resume_scans_per_month', value: '25' }],
                },
            ],
        },
    ];
    for (const plan of defaultPlans) {
        const existingPlan = await prisma.plan.findUnique({
            where: { id: plan.id },
        });
        if (!existingPlan) {
            await prisma.plan.create({
                data: {
                    id: plan.id,
                    name: plan.name,
                    description: plan.description,
                    section: plan.section,
                    monthlyPrice: plan.monthlyPrice,
                    annualPrice: plan.annualPrice,
                    stripePriceMonthlyId: null,
                    stripePriceYearlyId: null,
                    popular: plan.popular,
                },
            });
            console.log(`Created plan: ${plan.name}`);
        }
        else {
            console.log(`Plan already exists: ${plan.name}`);
        }
        // Seed plan features
        if (plan.features) {
            for (const feature of plan.features) {
                const existingPlanFeature = await prisma.planFeature.findFirst({
                    where: {
                        planId: plan.id,
                        featureId: feature.featureId,
                    },
                });
                if (!existingPlanFeature) {
                    const planFeature = await prisma.planFeature.create({
                        data: {
                            planId: plan.id,
                            featureId: feature.featureId,
                            accessLevel: feature.accessLevel,
                        },
                    });
                    console.log(`Created plan feature: ${plan.name} - ${feature.featureId}`);
                    // Seed plan feature limits
                    if (feature.limits) {
                        for (const limit of feature.limits) {
                            await prisma.planFeatureLimit.create({
                                data: {
                                    planFeatureId: planFeature.id,
                                    limitId: limit.limitId,
                                    value: limit.value,
                                },
                            });
                            console.log(`Created plan feature limit: ${plan.name} - ${feature.featureId} - ${limit.limitId}`);
                        }
                    }
                }
                else {
                    console.log(`Plan feature already exists: ${plan.name} - ${feature.featureId}`);
                }
            }
        }
    }
    console.log('Seeding completed!');
}
main()
    .catch((e) => {
    console.error(e);
    process.exit(1);
})
    .finally(async () => {
    await prisma.$disconnect();
});
