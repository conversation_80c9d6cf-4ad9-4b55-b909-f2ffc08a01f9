// scraper/index.ts
// Main entry point for the scraper service
import { logger } from "./utils/logger.js";
import { executeCommandStreaming } from "./utils/executeCommand.js";
class ScraperService {
    isRunning = false;
    currentJob = null;
    constructor() {
        // Handle graceful shutdown
        process.on("SIGINT", () => this.shutdown());
        process.on("SIGTERM", () => this.shutdown());
    }
    async start() {
        logger.info("🚀 Starting scraper service...");
        this.isRunning = true;
        // Run the scraping cycle continuously
        while (this.isRunning) {
            try {
                // Run parallel job scraper
                await this.runJob("parallel-scraper", ["npm", "run", "parallel"]);
                if (!this.isRunning)
                    break;
                // Wait between cycles
                logger.info("⏳ Waiting 2 hours before next scraping cycle...");
                await this.delay(7200000); // 2 hours
                // Run job details scraper
                await this.runJob("details-scraper", ["npm", "run", "details"]);
                if (!this.isRunning)
                    break;
                // Wait between cycles
                await this.delay(1800000); // 30 minutes
                // Run job enricher
                await this.runJob("job-enricher", ["npm", "run", "enrich"]);
                if (!this.isRunning)
                    break;
                // Wait before next full cycle
                logger.info("⏳ Waiting 4 hours before next full cycle...");
                await this.delay(14400000); // 4 hours
            }
            catch (error) {
                logger.error("❌ Error in scraping cycle:", error);
                // Wait before retrying
                await this.delay(600000); // 10 minutes
            }
        }
    }
    async runJob(jobName, command) {
        this.currentJob = jobName;
        logger.info(`🔄 Starting ${jobName}...`);
        try {
            await executeCommandStreaming(process.cwd(), command[0], command.slice(1), jobName);
            logger.info(`✅ ${jobName} completed successfully`);
        }
        catch (error) {
            logger.error(`❌ ${jobName} failed:`, error);
            throw error;
        }
        finally {
            this.currentJob = null;
        }
    }
    delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
    async shutdown() {
        logger.info("🛑 Shutting down scraper service...");
        this.isRunning = false;
        if (this.currentJob) {
            logger.info(`⏳ Waiting for current job (${this.currentJob}) to complete...`);
            // Give current job up to 5 minutes to complete
            let waitTime = 0;
            while (this.currentJob && waitTime < 300000) {
                await this.delay(5000);
                waitTime += 5000;
            }
        }
        logger.info("✅ Scraper service shut down gracefully");
        process.exit(0);
    }
}
// Start the service
const scraperService = new ScraperService();
scraperService.start().catch((error) => {
    logger.error("❌ Failed to start scraper service:", error);
    process.exit(1);
});
