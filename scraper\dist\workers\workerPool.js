import { logger } from "../utils/logger";
import { EventEmitter } from "events";
import { launchStealthBrowser, createStealthContext, applyStealthFingerprinting, } from "../utils/playwrightStealth.js";
import { handleCaptchaIfPresent } from "../scripts/improvedCaptchaSolver.js";
// Import headers utility
import { getRandomHeaders, getDefaultUserAgent } from "../utils/headers.js";
import { generateRandomFingerprint, applyFingerprint, } from "../utils/browserFingerprint.js";
// import { applyAdvancedFingerprinting } from "../utils/advancedFingerprinting";
import { rotateProxyWithIPCheck } from "../utils/proxyRotator.js";
import { processJobForOccupation } from "./jobScraperWorker.js";
export class WorkerPool extends EventEmitter {
    workers = new Map();
    maxWorkers;
    currentMaxWorkers; // Current maximum workers (can be adjusted dynamically)
    headless;
    slowMo;
    recycleThreshold;
    isShuttingDown = false;
    healthCheckInterval = null;
    HEALTH_CHECK_INTERVAL_MS = 1800000; // Check worker health every 30 minutes
    // PROXY
    SMARTPROXY_USERNAME = process.env.SMARTPROXY_USERNAME;
    SMARTPROXY_PASSWORD = process.env.SMARTPROXY_PASSWORD;
    SMARTPROXY_HOST = process.env.SMARTPROXY_HOST;
    SMARTPROXY_COUNT = process.env.SMARTPROXY_COUNT;
    assignedProxies = new Map();
    usedPorts = new Set(this.assignedProxies.values());
    constructor(options) {
        super();
        this.maxWorkers = options.maxWorkers;
        this.currentMaxWorkers = options.maxWorkers; // Initialize to the same as maxWorkers
        this.headless = options.headless;
        this.slowMo = options.slowMo || 50;
        this.recycleThreshold = options.recycleThreshold || 10; // Reduced from 30 to 10 jobs before recycling to prevent memory leaks
        // Handle process exit to clean up resources
        process.on("SIGINT", async () => {
            logger.info("🛑 Received SIGINT signal, shutting down worker pool...");
            await this.shutdown();
            process.exit(0);
        });
        process.on("SIGTERM", async () => {
            logger.info("⚠️ Received SIGTERM signal, but ignoring it to keep the worker pool running...");
            // Do not shut down or exit - just continue running
        });
        logger.info(`🔧 Worker pool configured with ${this.maxWorkers} workers (headless: ${this.headless})`);
        setInterval(() => {
            this.workers.forEach((worker) => {
                if (!worker.busy) {
                    void this.rotateProxyForWorker(worker);
                }
            });
        }, 15 * 60 * 1000); // every 15 minutes
    }
    /**
     * Initialize the worker pool with a specified number of workers
     */
    async initialize() {
        logger.info(`🚀 Initializing worker pool with ${this.maxWorkers} workers...`);
        const BATCH_SIZE = 5;
        for (let i = 0; i < this.maxWorkers; i += BATCH_SIZE) {
            const batch = Array.from({ length: BATCH_SIZE })
                .map((_, j) => i + j)
                .filter((index) => index < this.maxWorkers); // stay within bounds
            logger.info(`⚙️ Creating batch: Workers ${batch.join(", ")}`);
            // Create workers in parallel
            const results = await Promise.allSettled(batch.map(async (id) => {
                // No delay, create worker immediately
                logger.info(`🕐 Creating worker #${id} immediately...`);
                return this.createWorker(id);
            }));
            results.forEach((result, idx) => {
                const workerId = batch[idx];
                if (result.status === "rejected") {
                    logger.error(`❌ Failed to create worker #${workerId}: ${result.reason}`);
                }
            });
            logger.info(`📊 Progress: Created ${this.workers.size}/${this.maxWorkers} workers`);
            const successfulWorkers = this.workers.size;
            const failedWorkers = this.maxWorkers - successfulWorkers;
            if (successfulWorkers === this.maxWorkers) {
                logger.info(`✅ Worker pool initialized with all ${this.workers.size} workers`);
            }
            else {
                logger.warn(`⚠️ Worker pool initialized with ${successfulWorkers}/${this.maxWorkers} workers (${failedWorkers} failed)`);
                if (successfulWorkers === 0) {
                    throw new Error("Failed to create any workers. Check system resources and browser installation.");
                }
            }
            this.startHealthCheck();
        }
    }
    /**
     * Create a new worker with a browser instance
     */
    createWorker(id) {
        return new Promise(async (resolve, reject) => {
            try {
                logger.info(`🔧 Creating worker #${id}...`);
                const port = this.assignProxyToWorker(id);
                const proxyServer = `http://${this.SMARTPROXY_HOST}:${port}`;
                const proxyUrl = `http://${this.SMARTPROXY_USERNAME}:${this.SMARTPROXY_PASSWORD}@${this.SMARTPROXY_HOST}:${port}`;
                logger.info(`🌍 Using Smartproxy for worker #${id} → ${proxyUrl}`);
                // ✅ Launch stealth browser with only the proxy server (no auth)
                const browser = await launchStealthBrowser(this.headless, this.slowMo, proxyServer);
                logger.info(`✅ Browser launched for worker #${id}`);
                // ✅ Generate a completely random fingerprint for better evasion
                const fingerprint = generateRandomFingerprint();
                // ✅ Add proxy auth in the context with advanced fingerprinting
                const context = await createStealthContext(browser, {
                    httpCredentials: {
                        username: this.SMARTPROXY_USERNAME,
                        password: this.SMARTPROXY_PASSWORD,
                    },
                    viewport: fingerprint.viewport,
                    // Using FingerprintJS for user agent consistency
                    deviceScaleFactor: fingerprint.deviceScaleFactor,
                    locale: fingerprint.locale,
                    timezoneId: fingerprint.timezoneId,
                });
                logger.info(`✅ Created context with viewport ${fingerprint.viewport?.width || 1280}x${fingerprint.viewport?.height || 720} for worker #${id}`);
                const page = await context.newPage();
                // Apply basic fingerprinting techniques
                await applyFingerprint(context, page, fingerprint);
                // Apply advanced stealth fingerprinting with FingerprintJS
                await applyStealthFingerprinting(context, page, {
                    userAgent: getDefaultUserAgent(),
                    viewport: fingerprint.viewport,
                });
                // ⚡ Measure proxy latency and check if proxy is down
                // List of IP checking services to try
                const ipCheckServices = [
                    { url: "https://api.ipify.org", name: "ipify" },
                    { url: "https://ifconfig.me/ip", name: "ifconfig.me" },
                    { url: "https://icanhazip.com", name: "icanhazip" },
                    { url: "https://ipinfo.io/json", name: "ipinfo" },
                    {
                        url: "https://www.cloudflare.com/cdn-cgi/trace",
                        name: "cloudflare",
                    },
                    { url: "https://www.bing.com", name: "bing" },
                    { url: "https://www.yahoo.com", name: "yahoo" },
                ];
                let proxyWorking = false;
                // Try each service until one works
                for (const service of ipCheckServices) {
                    const start = Date.now();
                    try {
                        logger.info(`🔍 Worker #${id} testing proxy with ${service.name}...`);
                        const response = await page.goto(service.url, {
                            waitUntil: "domcontentloaded",
                            timeout: 15000,
                        });
                        const latency = Date.now() - start;
                        const status = response?.status() ?? "unknown";
                        logger.info(`⚡ Worker #${id} proxy latency: ${latency}ms (status: ${status}) with ${service.name}`);
                        if (latency > 8000) {
                            logger.warn(`🐌 Worker #${id} proxy is slow (>8s) with ${service.name}, may impact scraping`);
                        }
                        // If we get here, at least one service worked
                        proxyWorking = true;
                        break;
                    }
                    catch (err) {
                        const latency = Date.now() - start;
                        logger.warn(`⚠️ Worker #${id} failed with ${service.name} after ${latency}ms: ${err}`);
                        // Continue to the next service
                    }
                }
                if (!proxyWorking) {
                    logger.error(`⚠️ Worker #${id} failed all proxy checks. Proxy may be down.`);
                    // Try one more time with a direct connection to a major site
                    try {
                        const start = Date.now();
                        await page.goto("https://www.google.com", {
                            waitUntil: "domcontentloaded",
                            timeout: 15000,
                        });
                        const latency = Date.now() - start;
                        logger.info(`⚡ Worker #${id} connected to Google directly: ${latency}ms`);
                        proxyWorking = true;
                    }
                    catch (finalErr) {
                        logger.error(`💥 Proxy appears to be down. Shutting down worker pool.`);
                        await browser.close().catch(() => { });
                        // Throw a specific error to indicate proxy is down
                        reject(new Error(`Proxy is down: ${finalErr instanceof Error
                            ? finalErr.message
                            : String(finalErr)}`));
                        return;
                    }
                }
                // Replace context/page in worker instance
                const worker = await this.setupWorker(id, browser, context, page);
                resolve(worker);
            }
            catch (error) {
                logger.error(`❌ Failed to create worker #${id}: ${error}`);
                reject(error);
            }
        });
    }
    /**
     * Set up a worker with the given browser
     */
    async setupWorker(id, browser, context, page) {
        try {
            // 📑 Set improved headers for better fingerprinting
            logger.info(`🔧 Setting HTTP headers for worker #${id}...`);
            await page.setExtraHTTPHeaders(getRandomHeaders());
            logger.info(`🔧 HTTP headers set successfully for worker #${id}`);
            if (page.url() === "about:blank") {
                logger.info(`🔧 Skipping page navigation test for worker #${id} — already on about:blank`);
            }
            else {
                logger.info(`🔧 Skipping redundant page navigation for worker #${id}`);
            }
            // ✅ Return assembled worker
            const worker = {
                id,
                browser,
                context,
                page,
                busy: false,
                lastUsed: new Date(),
                captchaCount: 0,
                jobsProcessed: 0, // Initialize jobs processed counter
            };
            this.workers.set(worker.id, worker);
            logger.info(`✅ Worker #${id} created successfully`);
            return worker;
        }
        catch (error) {
            logger.error(`❌ Error setting up worker #${id}: ${error}`);
            try {
                await browser.close();
                logger.info(`🔧 Closed browser after failed setup for worker #${id}`);
            }
            catch (closeErr) {
                logger.error(`❌ Failed to close browser for worker #${id}: ${closeErr}`);
            }
            throw error;
        }
    }
    /**
     * Get a specific worker from the pool by index
     */
    async getSpecificWorker(workerIndex) {
        if (this.isShuttingDown) {
            return null;
        }
        // Check if we have any workers at all
        if (this.workers.size === 0) {
            logger.error(`❌ No workers available in the pool. Attempting to initialize...`);
            try {
                // Try to initialize the worker pool if it's empty
                await this.initialize();
                if (this.workers.size === 0) {
                    logger.error(`❌ Failed to initialize any workers`);
                    return null;
                }
            }
            catch (error) {
                logger.error(`❌ Failed to initialize worker pool: ${error}`);
                return null;
            }
        }
        // Find the worker with the specified index
        const worker = this.workers.get(workerIndex);
        let selectedWorker = null;
        // If the specific worker exists and is not busy, use it
        if (worker && !worker.busy) {
            // Verify the worker is in a good state before returning it
            try {
                // Check if the page is still valid
                const isPageValid = await worker.page
                    .evaluate(() => true)
                    .catch(() => false);
                if (!isPageValid) {
                    logger.warn(`⚠️ Worker #${worker.id} has an invalid page, rotating proxy...`);
                    try {
                        await this.rotateProxyForWorker(worker);
                        logger.info(`✅ Worker #${worker.id} rotated proxy after invalid page`);
                        // Worker is valid after rotation
                        selectedWorker = worker;
                    }
                    catch (error) {
                        logger.error(`❌ Failed to rotate proxy for worker #${worker.id}: ${error}`);
                        // Worker is invalid, try to recreate it
                        try {
                            await this.recreateWorker(worker.id);
                            selectedWorker = this.workers.get(worker.id) || null;
                            if (selectedWorker) {
                                logger.info(`✅ Successfully recreated worker #${worker.id}`);
                            }
                        }
                        catch (recreateError) {
                            logger.error(`❌ Failed to recreate worker #${worker.id}: ${recreateError}`);
                            selectedWorker = null;
                        }
                    }
                }
                else {
                    // Worker is valid
                    selectedWorker = worker;
                }
            }
            catch (error) {
                logger.error(`❌ Error checking worker #${worker.id} state: ${error}`);
                // Worker is invalid, try to recreate it
                try {
                    await this.recreateWorker(worker.id);
                    selectedWorker = this.workers.get(worker.id) || null;
                    if (selectedWorker) {
                        logger.info(`✅ Successfully recreated worker #${worker.id}`);
                    }
                }
                catch (recreateError) {
                    logger.error(`❌ Failed to recreate worker #${worker.id}: ${recreateError}`);
                    selectedWorker = null;
                }
            }
            // If worker is valid, mark it as busy
            if (selectedWorker) {
                selectedWorker.busy = true;
                selectedWorker.lastUsed = new Date();
                logger.info(`🔴 Assigned specific worker #${selectedWorker.id} (${Array.from(this.workers.values()).filter((w) => w.busy).length}/${this.workers.size} workers now busy)`);
                return selectedWorker;
            }
        }
        // If the specific worker doesn't exist, is busy, or is invalid, find any available worker
        const availableWorker = Array.from(this.workers.values()).find((w) => !w.busy);
        if (availableWorker) {
            // If an available worker is found, mark it as busy and return it
            availableWorker.busy = true;
            availableWorker.lastUsed = new Date();
            logger.info(`🔴 Requested worker #${workerIndex} is unavailable, using available worker #${availableWorker.id} instead (${Array.from(this.workers.values()).filter((w) => w.busy).length}/${this.workers.size} workers now busy)`);
            return availableWorker;
        }
        else {
            // If no workers are available, try to create a new one
            logger.warn(`⚠️ No available workers found when requesting worker #${workerIndex}. Attempting to create a new worker...`);
            try {
                // Find the highest worker ID currently in use
                const highestId = Math.max(-1, ...Array.from(this.workers.keys()));
                const newId = highestId + 1;
                // Create a new worker
                const newWorker = await this.createWorker(newId);
                newWorker.busy = true;
                newWorker.lastUsed = new Date();
                logger.info(`✅ Created and assigned new worker #${newId}`);
                return newWorker;
            }
            catch (error) {
                logger.error(`❌ Failed to create a new worker: ${error}`);
                return null;
            }
        }
    }
    /**
     * Get an available worker from the pool
     */
    async getWorker(workerId) {
        if (this.isShuttingDown) {
            return null;
        }
        // Fetch the worker by its ID from the Map
        const worker = this.workers.get(workerId);
        if (worker && !worker.busy) {
            // If the worker is not busy, mark it as busy and return it
            worker.busy = true;
            worker.lastUsed = new Date();
            return worker;
        }
        // If worker is busy or doesn't exist, try to get an available worker
        const availableWorker = Array.from(this.workers.values()).find((w) => !w.busy);
        if (availableWorker) {
            // If an available worker is found, mark it as busy and return it
            availableWorker.busy = true;
            availableWorker.lastUsed = new Date();
            return availableWorker;
        }
        // If all workers are busy, wait for one to become available
        logger.info("⏳ All workers are busy, waiting for one to become available...");
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                logger.warn("⚠️ Worker availability timed out.");
                resolve(null);
            }, 60000); // Wait up to 60 seconds
            const handleWorkerAvailable = () => {
                clearTimeout(timeout);
                const availableWorker = Array.from(this.workers.values()).find((w) => !w.busy);
                resolve(availableWorker || null);
            };
            this.once("worker-available", handleWorkerAvailable);
        });
    }
    /**
     * Release a worker back to the pool with improved memory management
     */
    releaseWorker(workerId) {
        const workerIndex = Array.from(this.workers.values()).findIndex((w) => w.id === workerId);
        if (workerIndex !== -1) {
            const worker = this.workers.get(workerId);
            if (worker && worker.busy) {
                // Only release if currently busy
                worker.busy = false;
                worker.lastUsed = new Date();
                // Increment jobs processed counter
                worker.jobsProcessed++;
                // Clear the worker's current job and batch references
                worker.currentBatch = undefined;
                worker.currentJob = undefined;
                // Check if worker is marked for shutdown (due to worker count reduction)
                if (worker.markedForShutdown) {
                    logger.info(`🔄 Worker #${workerId} was marked for shutdown, shutting down now`);
                    // Schedule shutdown asynchronously to not block the release
                    setTimeout(async () => {
                        try {
                            await this.shutdownWorker(worker);
                            this.workers.delete(workerId);
                            logger.info(`✅ Successfully shut down worker #${workerId} that was marked for shutdown`);
                        }
                        catch (error) {
                            logger.error(`❌ Error shutting down worker #${workerId}: ${error}`);
                        }
                    }, 1000);
                    // Don't emit worker-available since we're shutting it down
                    return;
                }
                // Check memory usage to determine if we should recycle more aggressively
                const memoryUsage = process.memoryUsage();
                const rssUsedMB = Math.round(memoryUsage.rss / 1024 / 1024);
                const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
                // Enhanced memory usage checks
                const highRssMemory = rssUsedMB > 1024; // Over 1GB RSS is considered high
                const highHeapMemory = heapUsedMB > 512; // Over 512MB heap is considered high
                const highMemoryUsage = highRssMemory || highHeapMemory;
                // Calculate worker age in minutes
                const workerAgeMinutes = (Date.now() - worker.lastUsed.getTime()) / (1000 * 60);
                // Enhanced recycling criteria:
                // 1. Jobs processed threshold
                // 2. High memory usage
                // 3. Worker age (recycle workers older than 30 minutes)
                // 4. Captcha count (recycle workers that have encountered multiple captchas)
                const shouldRecycle = worker.jobsProcessed >= this.recycleThreshold ||
                    highMemoryUsage ||
                    workerAgeMinutes > 30 ||
                    worker.captchaCount >= 2;
                // Determine the reason for recycling for logging
                let recycleReason = "";
                if (worker.jobsProcessed >= this.recycleThreshold) {
                    recycleReason = `processed ${worker.jobsProcessed} jobs`;
                }
                else if (highRssMemory) {
                    recycleReason = `high RSS memory usage (${rssUsedMB}MB)`;
                }
                else if (highHeapMemory) {
                    recycleReason = `high heap memory usage (${heapUsedMB}MB)`;
                }
                else if (workerAgeMinutes > 30) {
                    recycleReason = `worker age (${Math.round(workerAgeMinutes)} minutes)`;
                }
                else if (worker.captchaCount >= 2) {
                    recycleReason = `captcha encounters (${worker.captchaCount})`;
                }
                if (shouldRecycle) {
                    logger.info(`🔄 Worker #${workerId} will be recycled due to ${recycleReason}`);
                    // Schedule worker recycling asynchronously to not block the release
                    setTimeout(async () => {
                        if (!worker.busy) {
                            try {
                                // Clear page content and run garbage collection before recycling
                                await worker.page
                                    .evaluate(() => {
                                    // Clear DOM content
                                    document.body.innerHTML = "";
                                    // Clear any large objects that might be in memory
                                    if (window.localStorage) {
                                        window.localStorage.clear();
                                    }
                                    if (window.sessionStorage) {
                                        window.sessionStorage.clear();
                                    }
                                    // Remove all event listeners
                                    const oldElement = document.documentElement;
                                    const newElement = oldElement.cloneNode(true);
                                    if (oldElement.parentNode) {
                                        oldElement.parentNode.replaceChild(newElement, oldElement);
                                    }
                                    // Clear any timers
                                    const highestTimeoutId = setTimeout(() => { }, 0);
                                    for (let i = 0; i < highestTimeoutId; i++) {
                                        clearTimeout(i);
                                        clearInterval(i);
                                    }
                                    // Run browser garbage collection if available
                                    if (window.gc)
                                        window.gc();
                                    return true;
                                })
                                    .catch((err) => {
                                    logger.warn(`⚠️ Failed to clear page content for worker #${workerId}: ${err}`);
                                });
                                // Navigate to about:blank to further reduce memory usage
                                await worker.page
                                    .goto("about:blank", { timeout: 5000 })
                                    .catch((err) => {
                                    logger.warn(`⚠️ Failed to navigate to about:blank: ${err}`);
                                });
                                // Run Node.js garbage collection if available
                                if (global.gc) {
                                    try {
                                        global.gc();
                                        logger.info(`🧹 Ran garbage collection before recycling worker #${workerId}`);
                                    }
                                    catch (err) {
                                        logger.warn(`⚠️ Failed to run garbage collection: ${err}`);
                                    }
                                }
                                // Recreate the worker
                                await this.recreateWorker(workerId);
                                logger.info(`✅ Successfully recycled worker #${workerId}`);
                            }
                            catch (err) {
                                logger.error(`❌ Failed to recycle worker #${workerId}: ${err}`);
                                // If recycling fails, try to recreate the worker from scratch
                                try {
                                    // Force close the browser
                                    await worker.browser.close().catch(() => { });
                                    this.workers.delete(workerId);
                                    // Create a new worker with the same ID
                                    const newWorker = await this.createWorker(workerId);
                                    this.workers.set(workerId, newWorker);
                                    logger.info(`✅ Created new worker #${workerId} after recycling failure`);
                                }
                                catch (createErr) {
                                    logger.error(`❌ Failed to create new worker after recycling failure: ${createErr}`);
                                }
                            }
                        }
                        else {
                            logger.info(`⏳ Worker #${workerId} is busy, postponing recycling`);
                            // Mark for recycling on next release
                            worker.markedForRecycling = true;
                        }
                    }, 1000);
                }
                else if (worker.markedForRecycling) {
                    // Handle workers that were previously marked for recycling
                    logger.info(`🔄 Processing delayed recycling for worker #${workerId}`);
                    worker.markedForRecycling = false;
                    // Use the same recycling logic as above
                    setTimeout(async () => {
                        if (!worker.busy) {
                            try {
                                await worker.page
                                    .evaluate(() => {
                                    document.body.innerHTML = "";
                                    return true;
                                })
                                    .catch(() => { });
                                await this.recreateWorker(workerId);
                                logger.info(`✅ Successfully completed delayed recycling of worker #${workerId}`);
                            }
                            catch (err) {
                                logger.error(`❌ Failed delayed recycling of worker #${workerId}: ${err}`);
                            }
                        }
                    }, 1000);
                }
                // Check if we have more workers than the current max
                if (this.workers.size > this.currentMaxWorkers) {
                    // Only consider shutting down if we're not already recycling
                    if (!shouldRecycle) {
                        logger.info(`🔄 Worker pool has ${this.workers.size} workers but current max is ${this.currentMaxWorkers}`);
                        this.releaseExcessWorkers();
                    }
                }
                this.emit("worker-available");
                logger.info(`🔓 Worker #${workerId} released back to the pool`);
            }
        }
    }
    /**
     * Handle CAPTCHA detection and worker rotation
     */
    async handleCaptcha(worker) {
        const { captchaDetected } = await handleCaptchaIfPresent(worker.page);
        if (!captchaDetected)
            return false;
        logger.warn(`🧩 CAPTCHA detected on worker #${worker.id}! Rotating fingerprint...`);
        // Increment CAPTCHA count
        worker.captchaCount++;
        // Recreate full worker if it's failing too often
        if (worker.captchaCount >= 3) {
            await this.recreateWorker(worker.id);
            return true;
        }
        try {
            logger.info(`🔁 Rotating headers and proxy for worker #${worker.id}...`);
            await this.rotateProxyForWorker(worker);
            logger.info(`✅ Worker #${worker.id} fingerprint and proxy rotated. Ready to retry.`);
            // Mark worker as not busy but don't release it yet
            worker.busy = false;
            // Re-add worker to the pool
            this.workers.set(worker.id, worker);
            const occupation = worker.currentJob;
            const batch = worker.currentBatch;
            const retryResult = await processJobForOccupation(worker, occupation, batch);
            if (retryResult.success) {
                logger.info(`✅ Retried job for occupation ${occupation.title} successfully`);
            }
            else {
                logger.error(`❌ Failed to retry job for occupation ${occupation.title}`);
            }
            // Log that we're done with CAPTCHA handling, but don't release the worker again
            logger.info(`✅ Worker #${worker.id} completed CAPTCHA handling.`);
            return true;
        }
        catch (error) {
            logger.error(`❌ Failed rotating worker #${worker.id}: ${error}`);
            await this.recreateWorker(worker.id);
            return true;
        }
    }
    /**
     * Recreate a worker (close and create a new one)
     */
    async recreateWorker(workerId) {
        const worker = this.workers.get(workerId);
        logger.info(`🔄 Recreating worker #${workerId}...`);
        try {
            if (worker) {
                // Properly clean up resources to prevent memory leaks
                try {
                    // Close all pages except the main one
                    const pages = worker.context.pages();
                    for (const page of pages) {
                        if (page !== worker.page) {
                            await page
                                .close()
                                .catch((err) => logger.warn(`⚠️ Error closing extra page for worker #${workerId}: ${err}`));
                        }
                    }
                    // Close the main page
                    await worker.page
                        .close()
                        .catch((err) => logger.warn(`⚠️ Error closing main page for worker #${workerId}: ${err}`));
                    // Close the context
                    await worker.context
                        .close()
                        .catch((err) => logger.warn(`⚠️ Error closing context for worker #${workerId}: ${err}`));
                    // Close the browser
                    await worker.browser
                        .close()
                        .catch((err) => logger.warn(`⚠️ Error closing browser for worker #${workerId}: ${err}`));
                    // Force garbage collection if available
                    if (global.gc) {
                        global.gc();
                        logger.info(`🧹 Garbage collection triggered after closing worker #${workerId}`);
                    }
                }
                catch (cleanupError) {
                    logger.error(`❌ Error during cleanup for worker #${workerId}: ${cleanupError}`);
                }
            }
        }
        catch (error) {
            logger.error(`❌ Error closing browser for worker #${workerId}: ${error}`);
        }
        // Remove the old worker
        this.workers.delete(workerId);
        try {
            // 🔥 IMPORTANT: Replace with new worker instance
            const newWorker = await this.createWorker(workerId);
            this.workers.set(workerId, newWorker); // ✅ add to pool again
            // Reset the jobs processed counter
            newWorker.jobsProcessed = 0;
            logger.info(`✅ Worker #${workerId} recreated successfully`);
        }
        catch (createError) {
            logger.error(`❌ Failed to create new worker #${workerId}: ${createError}`);
            // Try one more time after a short delay
            try {
                logger.info(`🔄 Retrying worker #${workerId} creation after failure...`);
                await new Promise((resolve) => setTimeout(resolve, 5000));
                const newWorker = await this.createWorker(workerId);
                this.workers.set(workerId, newWorker);
                newWorker.jobsProcessed = 0;
                logger.info(`✅ Worker #${workerId} recreated successfully on second attempt`);
            }
            catch (retryError) {
                logger.error(`❌ Failed to create new worker #${workerId} on retry: ${retryError}`);
            }
        }
    }
    /**
     * Shutdown the worker pool and close all browsers
     */
    async shutdown() {
        this.isShuttingDown = true;
        logger.info("🛑 Shutting down worker pool...");
        // Stop the health check interval
        this.stopHealthCheck();
        // Log memory usage before cleanup
        const initialMemory = process.memoryUsage();
        logger.info(`🧠 Memory usage before cleanup: RSS ${Math.round(initialMemory.rss / 1024 / 1024)}MB, Heap ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);
        const closePromises = Array.from(this.workers.values()).map(async (worker) => {
            try {
                // Properly clean up resources to prevent memory leaks
                try {
                    // Close all pages except the main one
                    const pages = worker.context.pages();
                    for (const page of pages) {
                        if (page !== worker.page) {
                            await page
                                .close()
                                .catch((err) => logger.warn(`⚠️ Error closing extra page for worker #${worker.id}: ${err}`));
                        }
                    }
                    // Close the main page
                    await worker.page
                        .close()
                        .catch((err) => logger.warn(`⚠️ Error closing main page for worker #${worker.id}: ${err}`));
                    // Close the context
                    await worker.context
                        .close()
                        .catch((err) => logger.warn(`⚠️ Error closing context for worker #${worker.id}: ${err}`));
                    // Close the browser
                    await worker.browser
                        .close()
                        .catch((err) => logger.warn(`⚠️ Error closing browser for worker #${worker.id}: ${err}`));
                    logger.info(`✅ Worker #${worker.id} resources closed`);
                }
                catch (cleanupError) {
                    logger.error(`❌ Error during cleanup for worker #${worker.id}: ${cleanupError}`);
                    // Try one more time to close the browser
                    try {
                        await worker.browser.close();
                    }
                    catch (finalError) {
                        logger.error(`❌ Final attempt to close browser for worker #${worker.id} failed: ${finalError}`);
                    }
                }
            }
            catch (error) {
                logger.error(`❌ Error closing browser for worker #${worker.id}: ${error}`);
            }
        });
        await Promise.all(closePromises);
        // Clear worker references to help garbage collection
        this.workers = new Map();
        // Force garbage collection if available
        if (global.gc) {
            global.gc();
            logger.info(`🧹 Garbage collection triggered during shutdown`);
            // Log memory usage after cleanup
            const finalMemory = process.memoryUsage();
            logger.info(`🧠 Memory usage after cleanup: RSS ${Math.round(finalMemory.rss / 1024 / 1024)}MB, Heap ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);
            logger.info(`🧠 Memory freed: RSS ${Math.round((initialMemory.rss - finalMemory.rss) / 1024 / 1024)}MB, Heap ${Math.round((initialMemory.heapUsed - finalMemory.heapUsed) / 1024 / 1024)}MB`);
        }
        logger.info("✅ Worker pool shutdown complete");
    }
    /**
     * Start the health check interval to periodically check worker health
     */
    startHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        logger.info(`🏥 Starting worker health check (interval: ${this.HEALTH_CHECK_INTERVAL_MS / 1000}s)`);
        this.healthCheckInterval = setInterval(() => {
            // Use void to handle the Promise without awaiting it
            void this.checkWorkersHealth();
        }, this.HEALTH_CHECK_INTERVAL_MS);
    }
    /**
     * Stop the health check interval
     */
    stopHealthCheck() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
            logger.info("🛑 Stopped worker health check");
        }
    }
    /**
     * Check the health of all workers and recreate any that are unresponsive
     */
    async checkWorkersHealth() {
        logger.info(`🏥 Performing health check on ${this.workers.size} workers...`);
        const unhealthyWorkers = [];
        const healthCheckPromises = Array.from(this.workers.values()).map(async (worker) => {
            if (worker.busy)
                return;
            try {
                // Check if the page is still responsive
                const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error("Health check timeout")), 10000));
                const navigationPromise = worker.page
                    .goto("about:blank", { timeout: 5000 })
                    .then(() => true)
                    .catch(() => false);
                const isHealthy = await Promise.race([
                    navigationPromise,
                    timeoutPromise,
                ]).catch(() => false);
                if (!isHealthy) {
                    logger.warn(`⚠️ Worker #${worker.id} is unresponsive. Recreating page...`);
                    // Close old page
                    await worker.page.close();
                    // Create new page with improved fingerprinting
                    const newPage = await worker.context.newPage();
                    await newPage.setExtraHTTPHeaders(getRandomHeaders());
                    // Assign new page
                    worker.page = newPage;
                    worker.lastUsed = new Date();
                    logger.info(`✅ Worker #${worker.id} page recreated during health check.`);
                }
            }
            catch (error) {
                logger.error(`❌ Error during health check for worker #${worker.id}: ${error}`);
                unhealthyWorkers.push(worker.id);
            }
        });
        await Promise.allSettled(healthCheckPromises);
        if (unhealthyWorkers.length > 0) {
            logger.warn(`⚠️ Found ${unhealthyWorkers.length} unhealthy workers - recreating them...`);
            for (const workerId of unhealthyWorkers) {
                try {
                    await this.recreateWorker(workerId);
                    logger.info(`✅ Successfully recreated unhealthy worker #${workerId}`);
                }
                catch (error) {
                    logger.error(`❌ Failed to recreate unhealthy worker #${workerId}: ${error}`);
                }
            }
        }
        else {
            logger.info(`✅ All workers are healthy`);
        }
        await this.ensureWorkerCount();
    }
    /**
     * Ensure we have the desired number of workers
     */
    async ensureWorkerCount() {
        const missingWorkers = this.maxWorkers - this.workers.size;
        if (missingWorkers > 0) {
            logger.info(`⚠️ Missing ${missingWorkers} workers - attempting to create them...`);
            // Find the highest worker ID currently in use
            const highestId = Math.max(-1, ...Array.from(this.workers.keys()));
            // Create the missing workers
            for (let i = 0; i < missingWorkers; i++) {
                const newId = highestId + i + 1;
                try {
                    await this.createWorker(newId);
                    logger.info(`✅ Successfully created missing worker #${newId}`);
                }
                catch (error) {
                    logger.error(`❌ Failed to create missing worker #${newId}: ${error}`);
                }
            }
        }
    }
    assignProxyToWorker(workerId) {
        const allPorts = Array.from({ length: Number(this.SMARTPROXY_COUNT) }, (_, i) => 10001 + i);
        const availablePorts = allPorts.filter((p) => !this.usedPorts.has(p));
        const port = availablePorts[Math.floor(Math.random() * availablePorts.length)] ??
            10001 + (workerId % Number(this.SMARTPROXY_COUNT));
        this.assignedProxies.set(workerId, port);
        this.usedPorts.add(port);
        return port;
    }
    async rotateProxyForWorker(worker) {
        if (!worker)
            return;
        // Track if worker was busy before rotation
        const wasBusy = worker.busy;
        // Mark worker as busy during rotation to prevent concurrent use
        worker.busy = true;
        // Set a timeout for the entire rotation process
        const rotationTimeout = setTimeout(() => {
            logger.error(`⏱️ Proxy rotation timeout for worker #${worker.id}`);
            // If rotation times out, we'll recreate the worker
            this.recreateWorker(worker.id).catch((err) => {
                logger.error(`❌ Failed to recreate worker after timeout: ${err}`);
            });
        }, 30000); // 30 second timeout
        logger.info(`🔁 Rotating proxy for worker #${worker.id} with IP verification...`);
        try {
            // 🧼 Cleanup old resources
            const currentPort = this.assignedProxies.get(worker.id);
            if (currentPort)
                this.usedPorts.delete(currentPort);
            this.assignedProxies.delete(worker.id);
            try {
                // Use Promise.race to add timeout to close operations
                await Promise.race([
                    worker.page.close(),
                    new Promise((resolve) => setTimeout(resolve, 5000)),
                ]);
                await Promise.race([
                    worker.context.close(),
                    new Promise((resolve) => setTimeout(resolve, 5000)),
                ]);
            }
            catch (err) {
                logger.warn(`⚠️ Failed to close old context/page for worker #${worker.id}: ${err}`);
            }
            // Assign a new port using the existing assignProxyToWorker function
            const newPort = this.assignProxyToWorker(worker.id);
            const proxyUrl = `http://${this.SMARTPROXY_USERNAME}:${this.SMARTPROXY_PASSWORD}@${this.SMARTPROXY_HOST}:${newPort}`;
            logger.info(`🌍 Rotated Smartproxy for worker #${worker.id} → ${proxyUrl}`);
            // ♻️ Reuse existing browser and create a new stealth context with improved fingerprinting
            // Generate a random viewport with max dimensions of 960x960
            const viewport = {
                width: Math.floor(Math.random() * 400) + 560, // 560-960 width
                height: Math.floor(Math.random() * 400) + 560, // 560-960 height
            };
            const context = (await Promise.race([
                createStealthContext(worker.browser, {
                    httpCredentials: {
                        username: this.SMARTPROXY_USERNAME,
                        password: this.SMARTPROXY_PASSWORD,
                    },
                    viewport,
                    userAgent: getDefaultUserAgent(),
                    // Add additional fingerprinting evasion
                    deviceScaleFactor: Math.random() > 0.5 ? 1 : 2,
                    locale: "en-US",
                    timezoneId: [
                        "America/New_York",
                        "America/Los_Angeles",
                        "America/Chicago",
                        "America/Denver",
                        "America/Phoenix",
                    ][Math.floor(Math.random() * 5)],
                }),
                new Promise((_, reject) => setTimeout(() => reject(new Error("Context creation timeout")), 10000)),
            ]));
            const page = (await Promise.race([
                context.newPage(),
                new Promise((_, reject) => setTimeout(() => reject(new Error("Page creation timeout")), 10000)),
            ]));
            // Set additional headers for better fingerprinting
            await page.setExtraHTTPHeaders(getRandomHeaders());
            // Apply advanced stealth fingerprinting with FingerprintJS
            await applyStealthFingerprinting(context, page, {
                userAgent: getDefaultUserAgent(),
                viewport: viewport,
            });
            logger.info(`✅ Created new context with viewport ${viewport.width}x${viewport.height} for worker #${worker.id}`);
            // ✅ Replace worker’s context + page in-place
            worker.context = context;
            worker.page = page;
            worker.lastUsed = new Date();
            worker.captchaCount = 0; // Reset captcha count after rotation
            // Restore previous busy state if worker wasn't busy before rotation
            if (!wasBusy) {
                worker.busy = false;
            }
            // Clear the rotation timeout
            clearTimeout(rotationTimeout);
            logger.info(`✅ Worker #${worker.id} proxy rotation completed`);
        }
        catch (error) {
            logger.error(`❌ Error rotating proxy for worker #${worker.id}: ${error}`);
            // Clear the timeout since we're handling the error
            clearTimeout(rotationTimeout);
            try {
                // If rotation fails, try to recreate the worker entirely
                await this.recreateWorker(worker.id);
                logger.info(`✅ Worker #${worker.id} recreated after failed proxy rotation`);
            }
            catch (recreateError) {
                logger.error(`❌ Failed to recreate worker #${worker.id}: ${recreateError}`);
            }
            finally {
                // If worker was not busy before, release it
                if (!wasBusy) {
                    worker.busy = false;
                }
            }
        }
    }
    /**
     * Enhanced proxy rotation with IP verification
     */
    async rotateProxyWithIPVerification(worker) {
        if (!worker)
            return false;
        logger.info(`🔁 Rotating proxy for worker #${worker.id} with IP verification...`);
        try {
            // Use the enhanced proxy rotation with IP verification
            const success = await rotateProxyWithIPCheck(worker, this.SMARTPROXY_USERNAME, this.SMARTPROXY_PASSWORD, this.SMARTPROXY_HOST, this.assignedProxies, this.usedPorts, (workerId) => this.assignProxyToWorker(workerId), 3 // Max retries
            );
            if (success) {
                logger.info(`✅ Worker #${worker.id} proxy rotation completed with IP verification`);
                return true;
            }
            else {
                logger.warn(`⚠️ Worker #${worker.id} proxy rotation failed with IP verification`);
                return false;
            }
        }
        catch (error) {
            logger.error(`❌ Error rotating proxy with IP verification for worker #${worker.id}: ${error}`);
            return false;
        }
    }
    getActiveWorkerCount() {
        let count = 0;
        for (const worker of this.workers.values()) {
            if (worker.busy)
                count++;
        }
        return count;
    }
    /**
     * Get the total number of workers
     */
    getTotalWorkerCount() {
        return this.workers.size;
    }
    /**
     * Get the current maximum worker count
     */
    getCurrentMaxWorkers() {
        return this.currentMaxWorkers;
    }
    /**
     * Set the maximum number of workers dynamically
     * This will not immediately create or destroy workers, but will affect future worker creation
     * @param count New maximum worker count
     */
    setMaxWorkers(count) {
        const oldCount = this.currentMaxWorkers;
        this.currentMaxWorkers = Math.max(1, Math.min(count, this.maxWorkers));
        if (oldCount !== this.currentMaxWorkers) {
            logger.info(`🔄 Adjusted maximum worker count from ${oldCount} to ${this.currentMaxWorkers}`);
            // If reducing workers, release excess workers
            if (this.currentMaxWorkers < oldCount) {
                this.releaseExcessWorkers();
            }
        }
    }
    /**
     * Release excess workers to match the current max worker count
     * This will gracefully shut down workers that exceed the current maximum
     */
    async releaseExcessWorkers() {
        if (this.workers.size <= this.currentMaxWorkers) {
            return; // No excess workers
        }
        const excessCount = this.workers.size - this.currentMaxWorkers;
        logger.info(`🔄 Releasing ${excessCount} excess workers...`);
        // Find workers to release (prefer idle workers first)
        const workersToRelease = [];
        // First, try to find idle workers
        for (const [id, worker] of this.workers.entries()) {
            if (!worker.busy && workersToRelease.length < excessCount) {
                workersToRelease.push(id);
            }
        }
        // If we still need more, add busy workers (they'll be released when they finish their current job)
        if (workersToRelease.length < excessCount) {
            for (const [id, worker] of this.workers.entries()) {
                if (worker.busy &&
                    !workersToRelease.includes(id) &&
                    workersToRelease.length < excessCount) {
                    workersToRelease.push(id);
                }
            }
        }
        // Release the workers
        for (const workerId of workersToRelease) {
            const worker = this.workers.get(workerId);
            if (!worker)
                continue;
            if (!worker.busy) {
                // If worker is idle, shut it down immediately
                logger.info(`🔄 Shutting down excess idle worker #${workerId}`);
                try {
                    await this.shutdownWorker(worker);
                    this.workers.delete(workerId);
                    logger.info(`✅ Successfully shut down excess worker #${workerId}`);
                }
                catch (error) {
                    logger.error(`❌ Error shutting down excess worker #${workerId}: ${error}`);
                }
            }
            else {
                // If worker is busy, mark it for shutdown when it's released
                logger.info(`🔄 Marking busy worker #${workerId} for shutdown when released`);
                worker.markedForShutdown = true;
            }
        }
    }
    /**
     * Shut down a specific worker and clean up its resources
     */
    async shutdownWorker(worker) {
        try {
            // Close page and context
            await Promise.allSettled([worker.page?.close(), worker.context?.close()]);
            // Release proxy
            const port = this.assignedProxies.get(worker.id);
            if (port) {
                this.usedPorts.delete(port);
                this.assignedProxies.delete(worker.id);
            }
            logger.info(`✅ Worker #${worker.id} resources cleaned up`);
        }
        catch (error) {
            logger.error(`❌ Error cleaning up worker #${worker.id}: ${error}`);
        }
    }
}
