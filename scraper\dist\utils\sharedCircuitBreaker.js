// cron/utils/sharedCircuitBreaker.ts
// Shared circuit breaker instance for all jobs to use
import { logger } from "./logger";
import { ImprovedImprovedCircuitBreaker, CircuitState, } from "./improvedImprovedCircuitBreaker";
import { config } from "../config";
import { sendEmailNotification, EmailNotificationType } from "./emailService";
import os from "os";
// Create a singleton instance of the circuit breaker
let circuitBreakerInstance = null;
/**
 * Get the shared circuit breaker instance
 * This ensures all jobs use the same circuit breaker
 */
export function getSharedCircuitBreaker() {
    if (!circuitBreakerInstance) {
        // Initialize the circuit breaker with configuration from config.ts
        circuitBreakerInstance = new ImprovedImprovedCircuitBreaker({
            memoryThresholdPercent: config.circuitBreaker.memoryThresholdPercent,
            cpuThresholdPercent: config.circuitBreaker.cpuThresholdPercent,
            errorThresholdCount: config.circuitBreaker.errorThresholdCount,
            resetTimeoutMs: config.circuitBreaker.resetTimeoutMs,
            checkIntervalMs: config.circuitBreaker.checkIntervalMs,
            consecutiveReadingsForOpen: config.circuitBreaker.consecutiveReadingsForOpen,
            consecutiveReadingsForClose: config.circuitBreaker.consecutiveReadingsForClose,
            consecutiveReadingsForDegraded: config.circuitBreaker.consecutiveReadingsForDegraded,
            degradedMemoryThresholdPercent: config.circuitBreaker.degradedMemoryThresholdPercent,
            degradedCpuThresholdPercent: config.circuitBreaker.degradedCpuThresholdPercent,
            onStateChange: (oldState, newState) => {
                // Log state changes
                if (newState === CircuitState.OPEN) {
                    logger.warn(`🔴 Circuit breaker opened - pausing job processing due to system constraints`);
                    // Send email notification about circuit breaker opening
                    sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
                        timestamp: new Date().toISOString(),
                        message: "Circuit breaker opened due to system resource constraints",
                        oldState,
                        newState,
                        memoryUsage: `${(((os.totalmem() - os.freemem()) / os.totalmem()) * 100).toFixed(2)}%`,
                        cpuUsage: `${((os.loadavg()[0] / os.cpus().length) * 100).toFixed(2)}%`,
                    }).catch((err) => logger.error(`Failed to send circuit breaker email: ${err}`));
                }
                else if (newState === CircuitState.CLOSED) {
                    // When circuit breaker closes (resets), log the event
                    logger.info(`🟢 Circuit breaker closed - resuming normal job processing`);
                    // This is where we would previously run the autoResetCircuitBreaker job
                    // Now we handle it directly here, which is more efficient
                    logger.info(`🔄 Circuit breaker has auto-reset itself - jobs will resume on their next scheduled run`);
                    // No need to restart jobs - they will run on their next scheduled time
                    // This is more reliable than forcing all jobs to restart at once
                }
                else if (newState === CircuitState.HALF_OPEN) {
                    logger.info(`🟡 Circuit breaker half-open - testing system recovery`);
                }
                else if (newState === CircuitState.DEGRADED) {
                    logger.info(`🟠 Circuit breaker in degraded mode - running with reduced capacity`);
                }
            },
        });
        logger.info("🔄 Shared circuit breaker initialized");
    }
    return circuitBreakerInstance;
}
/**
 * Reset the shared circuit breaker instance
 * This is useful for testing or when you want to force a reset
 */
export function resetSharedCircuitBreaker() {
    if (circuitBreakerInstance) {
        circuitBreakerInstance.resetErrors();
        logger.info("🔄 Shared circuit breaker reset");
    }
}
/**
 * Execute a function with circuit breaker protection
 * @param jobName Name of the job (for logging)
 * @param fn Function to execute
 * @returns Result of the function or null if circuit breaker is open
 */
export async function withCircuitBreaker(jobName, fn) {
    const circuitBreaker = getSharedCircuitBreaker();
    // Check if circuit breaker allows execution
    if (!(await circuitBreaker.isClosed())) {
        logger.warn(`⚠️ Circuit breaker is ${circuitBreaker.getState()}, skipping job ${jobName}`);
        return null;
    }
    try {
        logger.info(`🚀 Executing job ${jobName} with circuit breaker protection`);
        return await fn();
    }
    catch (error) {
        // Record error in circuit breaker
        circuitBreaker.recordError();
        logger.error(`❌ Error in job ${jobName}:`, error);
        throw error;
    }
}
