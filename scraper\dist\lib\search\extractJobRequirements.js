import { logger } from "../../utils/logger";
/**
 * Strip HTML tags from text
 */
function stripHtml(html) {
    return html
        .replace(/<[^>]*>/g, "") // Remove HTML tags
        .replace(/&nbsp;/g, " ") // Replace &nbsp; with space
        .replace(/&amp;/g, "&") // Replace &amp; with &
        .replace(/&lt;/g, "<") // Replace &lt; with <
        .replace(/&gt;/g, ">") // Replace &gt; with >
        .replace(/&quot;/g, '"') // Replace &quot; with "
        .replace(/&#39;/g, "'") // Replace &#39; with '
        .replace(/\s+/g, " ") // Replace multiple spaces with single space
        .trim(); // Trim whitespace
}
// Common technical skills that might appear in job descriptions
const technicalSkills = [
    "javascript",
    "typescript",
    "python",
    "java",
    "c#",
    "cplusplus", // Changed from c++ to avoid regex issues
    "ruby",
    "php",
    "swift",
    "kotlin",
    "react",
    "angular",
    "vue",
    "node",
    "express",
    "django",
    "flask",
    "spring",
    "asp.net",
    "html",
    "css",
    "sass",
    "less",
    "bootstrap",
    "tailwind",
    "material-ui",
    "jquery",
    "aws",
    "azure",
    "gcp",
    "docker",
    "kubernetes",
    "terraform",
    "jenkins",
    "circleci",
    "github actions",
    "sql",
    "mysql",
    "postgresql",
    "mongodb",
    "dynamodb",
    "redis",
    "elasticsearch",
    "rest",
    "graphql",
    "grpc",
    "soap",
    "microservices",
    "serverless",
    "git",
    "svn",
    "mercurial",
    "jira",
    "confluence",
    "trello",
    "asana",
    "agile",
    "scrum",
    "kanban",
    "waterfall",
    "tdd",
    "bdd",
    "ci/cd",
    "machine learning",
    "ai",
    "data science",
    "big data",
    "hadoop",
    "spark",
    "tensorflow",
    "pytorch",
    "mobile",
    "ios",
    "android",
    "react native",
    "flutter",
    "xamarin",
    "blockchain",
    "ethereum",
    "solidity",
    "web3",
    "nft",
    "security",
    "penetration testing",
    "encryption",
    "authentication",
    "authorization",
    "testing",
    "unit testing",
    "integration testing",
    "e2e testing",
    "selenium",
    "cypress",
    "jest",
    "mocha",
];
// Common soft skills that might appear in job descriptions
const softSkills = [
    "communication",
    "teamwork",
    "collaboration",
    "leadership",
    "problem solving",
    "critical thinking",
    "creativity",
    "time management",
    "organization",
    "adaptability",
    "flexibility",
    "resilience",
    "attention to detail",
    "analytical",
    "interpersonal",
    "presentation",
    "negotiation",
    "conflict resolution",
    "decision making",
    "mentoring",
];
/**
 * Extract requirements from a job description
 */
export function extractRequirements(description) {
    logger.info(`🔍 Extracting requirements from job description`);
    if (!description) {
        logger.info(`⚠️ No description provided for requirement extraction`);
        return [];
    }
    // Clean the description by removing HTML tags
    const cleanDescription = stripHtml(description);
    const requirements = [];
    // Look for requirements section
    const requirementSectionRegexes = [
        /requirements?:?(.*?)(?:responsibilities|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|what we offer|benefits|apply now|how to apply|application process)/is,
        /qualifications:?(.*?)(?:responsibilities|requirements|about you|what you'll do|what you will do|about the role|about the job|about the position|what we offer|benefits|apply now|how to apply|application process)/is,
        /what we('re| are) looking for:?(.*?)(?:responsibilities|requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|what we offer|benefits|apply now|how to apply|application process)/is,
        /what you('ll| will) need:?(.*?)(?:responsibilities|requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|what we offer|benefits|apply now|how to apply|application process)/is,
    ];
    let requirementsText = "";
    // Try to find a requirements section
    for (const regex of requirementSectionRegexes) {
        const match = cleanDescription.match(regex);
        if (match && match[1]) {
            requirementsText = match[1].trim();
            logger.info(`✅ Found requirements section with ${requirementsText.length} characters`);
            break;
        }
    }
    // If we couldn't find a dedicated section, use the whole description
    if (!requirementsText) {
        requirementsText = cleanDescription;
        logger.info(`⚠️ No specific requirements section found, using full description`);
    }
    // Look for bullet points or numbered lists
    const bulletPointRegex = /(?:^|\n)[\s•\-*+]+([^\n•\-*+][^\n]+)/g;
    const numberedListRegex = /(?:^|\n)[\s]*\d+\.[\s]+([^\n]+)/g;
    let match;
    // Extract bullet points
    while ((match = bulletPointRegex.exec(requirementsText)) !== null) {
        if (match[1] && match[1].trim().length > 10) {
            requirements.push(match[1].trim());
        }
    }
    // Extract numbered list items
    while ((match = numberedListRegex.exec(requirementsText)) !== null) {
        if (match[1] && match[1].trim().length > 10) {
            requirements.push(match[1].trim());
        }
    }
    // If we couldn't find bullet points or numbered lists, try to extract sentences
    if (requirements.length === 0) {
        // First, clean up the text by removing extra whitespace
        const cleanedText = requirementsText.replace(/\s+/g, " ").trim();
        // Split by sentence endings and filter out incomplete sentences
        const sentences = cleanedText
            .split(/[.!?]+/)
            .map((s) => s.trim())
            .filter((s) => 
        // Must be a reasonable length
        s.length > 15 &&
            s.length < 200 &&
            // Must start with a capital letter (likely a complete sentence)
            /^[A-Z]/.test(s) &&
            // Exclude sentences about applying
            !s.toLowerCase().includes("apply") &&
            !s.toLowerCase().includes("submit") &&
            !s.toLowerCase().includes("resume") &&
            !s.toLowerCase().includes("cv") &&
            // Exclude sentences that start with conjunctions or other fragments
            !/^(and|or|but|,|;|:)\s/i.test(s));
        // Add the filtered sentences to requirements
        requirements.push(...sentences);
        // If we still don't have requirements, try to extract paragraphs
        if (requirements.length === 0) {
            const paragraphs = cleanedText
                .split(/\n\n+/)
                .map((p) => p.trim())
                .filter((p) => p.length > 30 && p.length < 300);
            requirements.push(...paragraphs);
        }
    }
    // Limit to the most relevant requirements (first 10)
    const filteredRequirements = requirements
        .filter((req) => req.length > 10 && req.length < 200)
        .slice(0, 10);
    logger.info(`📋 Extracted ${filteredRequirements.length} requirements`);
    return filteredRequirements;
}
/**
 * Extract skills from a job description
 */
export function extractSkills(description) {
    logger.info(`🔍 Extracting skills from job description`);
    if (!description) {
        logger.info(`⚠️ No description provided for skill extraction`);
        return [];
    }
    // Clean the description by removing HTML tags
    const cleanDescription = stripHtml(description);
    const lowerCaseDescription = cleanDescription.toLowerCase();
    const foundSkills = [];
    // Look for technical skills
    technicalSkills.forEach((skill) => {
        try {
            // Special case for C++ which has special regex characters
            if (skill === "c++") {
                if (lowerCaseDescription.includes("c++")) {
                    foundSkills.push(skill);
                    logger.info(`🛠️ Found skill: ${skill}`);
                }
            }
            else {
                // Use word boundaries to ensure we're matching whole words
                const regex = new RegExp(`\\b${skill}\\b`, "i");
                if (regex.test(lowerCaseDescription)) {
                    foundSkills.push(skill);
                    logger.info(`🛠️ Found skill: ${skill}`);
                }
            }
        }
        catch (e) {
            logger.warn(`⚠️ Error checking for skill ${skill}: ${e.message}`);
        }
    });
    // Look for soft skills
    softSkills.forEach((skill) => {
        try {
            const regex = new RegExp(`\\b${skill}\\b`, "i");
            if (regex.test(lowerCaseDescription)) {
                foundSkills.push(skill);
                logger.info(`🛠️ Found soft skill: ${skill}`);
            }
        }
        catch (e) {
            logger.warn(`⚠️ Error checking for soft skill ${skill}: ${e.message}`);
        }
    });
    logger.info(`🛠️ Extracted ${foundSkills.length} skills`);
    return foundSkills;
}
/**
 * Get the maximum years of experience from a list of requirements
 */
export function getMaxYearsOfExperience(requirements) {
    if (!requirements || requirements.length === 0) {
        return 0;
    }
    // Find the maximum years of experience
    return Math.max(...requirements.map((req) => req.years));
}
export function extractYearsOfExperience(description) {
    logger.info(`🔍 Extracting years of experience from job description`);
    if (!description) {
        logger.info(`⚠️ No description provided for years of experience extraction`);
        return [];
    }
    // Clean the description by removing HTML tags
    const cleanDescription = stripHtml(description);
    const lowerCaseDescription = cleanDescription.toLowerCase();
    const experienceRequirements = [];
    const yearsFoundSet = new Set(); // For deduplication check
    // Common patterns for general years of experience
    const experiencePatterns = [
        /(\d+)[\s-]*\+?\s*years?\s+(?:of\s+)?experience/gi,
        /experience\s*(?:of|:)?\s*(\d+)[\s-]*\+?\s*years?/gi,
        /(\d+)[\s-]*\+?\s*years?\s+(?:of\s+)?(?:relevant|professional|industry|related)\s+experience/gi,
        /minimum\s+(?:of\s+)?(\d+)[\s-]*\+?\s*years?\s+(?:of\s+)?experience/gi,
        /at\s+least\s+(\d+)[\s-]*\+?\s*years?\s+(?:of\s+)?experience/gi,
        /(\d+)[\s-]*\+?\s*years?\s+(?:of\s+)?(?:work|working)\s+experience/gi,
    ];
    // Use global flag to find all matches
    for (const pattern of experiencePatterns) {
        let match;
        while ((match = pattern.exec(lowerCaseDescription)) !== null) {
            if (match && match[1]) {
                const years = parseInt(match[1], 10);
                if (!isNaN(years) &&
                    years > 0 &&
                    years < 30 &&
                    !yearsFoundSet.has(years)) {
                    // Sanity check and deduplicate
                    experienceRequirements.push({ years });
                    yearsFoundSet.add(years);
                    logger.info(`⏱️ Extracted ${years} years of experience`);
                }
            }
        }
    }
    // Look for specific skill-related experience
    // Improved pattern to capture more context
    const skillExperiencePatterns = [
        // Pattern for "X years of experience in/with [skill]"
        /(\d+)[\s-]*\+?\s*years?\s+(?:of\s+)?(?:.*?)\s+experience\s+(?:in|with)\s+(.*?)(?:\.|,|;)/gi,
        // Pattern for "experience with [skill] for X years"
        /experience\s+(?:in|with)\s+(.*?)\s+for\s+(\d+)[\s-]*\+?\s*years?/gi,
        // Pattern for "X+ years [skill] experience"
        /(\d+)[\s-]*\+?\s*years?\s+(.*?)\s+experience/gi,
    ];
    for (const pattern of skillExperiencePatterns) {
        let skillMatch;
        while ((skillMatch = pattern.exec(lowerCaseDescription)) !== null) {
            if (skillMatch) {
                // Handle different pattern formats
                let years, skill;
                if (pattern.toString().includes("for\\s+(\\d+)")) {
                    // Pattern: experience with [skill] for X years
                    skill = skillMatch[1]?.trim();
                    years = parseInt(skillMatch[2], 10);
                }
                else if (pattern
                    .toString()
                    .includes("(\\d+)[\\s-]*\\+?\\s*years?\\s+(.*?)\\s+experience")) {
                    // Pattern: X+ years [skill] experience
                    years = parseInt(skillMatch[1], 10);
                    skill = skillMatch[2]?.trim();
                }
                else {
                    // Default pattern: X years of experience in/with [skill]
                    years = parseInt(skillMatch[1], 10);
                    skill = skillMatch[2]?.trim();
                }
                if (!isNaN(years) &&
                    years > 0 &&
                    years < 30 &&
                    skill &&
                    skill.length > 0) {
                    // Add as a separate entry with the associated skill
                    experienceRequirements.push({ years, skill });
                    logger.info(`⏱️ Extracted ${years} years of experience with ${skill}`);
                }
            }
        }
    }
    if (experienceRequirements.length === 0) {
        logger.info(`⚠️ Could not extract years of experience`);
    }
    else {
        // Log a summary of the experience requirements
        const yearsOnly = experienceRequirements.map((req) => req.years);
        const uniqueYears = [...new Set(yearsOnly)];
        logger.info(`⏱️ Found ${experienceRequirements.length} experience requirements (${uniqueYears.length} different year values)`);
        // Log detailed experience requirements
        experienceRequirements.forEach((req) => {
            if (req.skill) {
                logger.info(`⏱️ ${req.years} years required for ${req.skill}`);
            }
            else {
                logger.info(`⏱️ ${req.years} years of general experience required`);
            }
        });
    }
    return experienceRequirements;
}
/**
 * Extract security clearance requirements from a job description
 */
export function extractSecurityClearance(description) {
    logger.info(`🔍 Extracting security clearance requirements from job description`);
    if (!description) {
        logger.info(`⚠️ No description provided for security clearance extraction`);
        return undefined;
    }
    const lowerCaseDescription = description.toLowerCase();
    // Common security clearance levels
    const clearanceLevels = [
        {
            name: "Top Secret/SCI",
            patterns: [
                "top secret[/\\s-]*sci",
                "ts[/\\s-]*sci",
                "top secret with sci",
                "ts with sci",
            ],
        },
        {
            name: "Top Secret",
            patterns: ["\\btop secret\\b", "\\bts\\b clearance"],
        },
        { name: "Secret", patterns: ["\\bsecret clearance\\b", "\\bsecret\\b"] },
        {
            name: "Confidential",
            patterns: ["\\bconfidential clearance\\b", "\\bconfidential\\b"],
        },
        { name: "Public Trust", patterns: ["\\bpublic trust\\b"] },
        {
            name: "Security Clearance Required",
            patterns: [
                "security clearance required",
                "must have clearance",
                "active clearance",
                "current clearance",
            ],
        },
    ];
    // Look for clearance requirements
    for (const level of clearanceLevels) {
        for (const pattern of level.patterns) {
            const regex = new RegExp(pattern, "i");
            if (regex.test(lowerCaseDescription)) {
                logger.info(`🔑 Extracted security clearance requirement: ${level.name}`);
                return level.name;
            }
        }
    }
    logger.info(`⚠️ No security clearance requirements found`);
    return undefined;
}
/**
 * Extract benefits from a job description
 */
export function extractBenefits(description) {
    logger.info(`🔍 Extracting benefits from job description`);
    if (!description) {
        logger.info(`⚠️ No description provided for benefits extraction`);
        return [];
    }
    const lowerCaseDescription = description.toLowerCase();
    const benefits = [];
    // Look for benefits section
    const benefitsSectionRegexes = [
        /benefits:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
        /perks:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
        /what we offer:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
        /compensation and benefits:?(.*?)(?:requirements|qualifications|about you|what you'll do|what you will do|about the role|about the job|about the position|how to apply|application process)/is,
    ];
    let benefitsText = "";
    // Try to find a benefits section
    for (const regex of benefitsSectionRegexes) {
        const match = description.match(regex);
        if (match && match[1]) {
            benefitsText = match[1].trim();
            logger.info(`✅ Found benefits section with ${benefitsText.length} characters`);
            break;
        }
    }
    // If we couldn't find a dedicated section, search the whole description
    if (!benefitsText) {
        benefitsText = description;
        logger.info(`⚠️ No specific benefits section found, using full description`);
    }
    // Common benefits to look for
    const commonBenefits = [
        {
            name: "Health Insurance",
            patterns: [
                "health insurance",
                "medical insurance",
                "medical coverage",
                "health benefits",
                "healthcare",
            ],
        },
        {
            name: "Dental Insurance",
            patterns: [
                "dental insurance",
                "dental coverage",
                "dental benefits",
                "dental",
            ],
        },
        {
            name: "Vision Insurance",
            patterns: [
                "vision insurance",
                "vision coverage",
                "vision benefits",
                "vision",
            ],
        },
        {
            name: "401(k)",
            patterns: ["401[\\(\\)k]", "retirement plan", "retirement benefits"],
        },
        {
            name: "Paid Time Off",
            patterns: [
                "paid time off",
                "pto",
                "vacation time",
                "vacation days",
                "paid vacation",
            ],
        },
        {
            name: "Remote Work",
            patterns: [
                "remote work",
                "work from home",
                "wfh",
                "telecommute",
                "flexible work location",
            ],
        },
        {
            name: "Flexible Hours",
            patterns: [
                "flexible hours",
                "flexible schedule",
                "flexible working hours",
                "work-life balance",
            ],
        },
        {
            name: "Parental Leave",
            patterns: [
                "parental leave",
                "maternity leave",
                "paternity leave",
                "family leave",
            ],
        },
        {
            name: "Professional Development",
            patterns: [
                "professional development",
                "training",
                "education reimbursement",
                "tuition reimbursement",
                "learning opportunities",
            ],
        },
        {
            name: "Stock Options",
            patterns: [
                "stock options",
                "equity",
                "esop",
                "stock purchase plan",
                "rsu",
            ],
        },
        {
            name: "Bonuses",
            patterns: ["bonus", "bonuses", "performance bonus", "annual bonus"],
        },
        {
            name: "Gym Membership",
            patterns: [
                "gym membership",
                "fitness stipend",
                "wellness program",
                "fitness benefits",
            ],
        },
        {
            name: "Commuter Benefits",
            patterns: [
                "commuter benefits",
                "transportation allowance",
                "transit benefits",
            ],
        },
        { name: "Life Insurance", patterns: ["life insurance"] },
        {
            name: "Disability Insurance",
            patterns: [
                "disability insurance",
                "short-term disability",
                "long-term disability",
            ],
        },
        {
            name: "Health Savings Account",
            patterns: [
                "health savings account",
                "hsa",
                "fsa",
                "flexible spending account",
            ],
        },
        {
            name: "Employee Assistance Program",
            patterns: ["employee assistance program", "eap", "counseling services"],
        },
        {
            name: "Relocation Assistance",
            patterns: [
                "relocation assistance",
                "relocation package",
                "moving assistance",
            ],
        },
    ];
    // Look for common benefits
    for (const benefit of commonBenefits) {
        for (const pattern of benefit.patterns) {
            const regex = new RegExp(`\\b${pattern}\\b`, "i");
            if (regex.test(lowerCaseDescription) &&
                !benefits.includes(benefit.name)) {
                benefits.push(benefit.name);
                logger.info(`💪 Found benefit: ${benefit.name}`);
                break; // Break after finding the first pattern for this benefit
            }
        }
    }
    // Look for bullet points in the benefits section that might be custom benefits
    const bulletPointRegex = /(?:^|\n)[\s•\-*+]+([^\n•\-*+][^\n]+)/g;
    let match;
    while ((match = bulletPointRegex.exec(benefitsText)) !== null) {
        if (match[1] &&
            match[1].trim().length > 5 &&
            match[1].trim().length < 100) {
            const customBenefit = match[1].trim();
            // Check if this benefit is not similar to any we've already found
            const isDuplicate = benefits.some((b) => customBenefit.toLowerCase().includes(b.toLowerCase()) ||
                b.toLowerCase().includes(customBenefit.toLowerCase()));
            if (!isDuplicate) {
                benefits.push(customBenefit);
                logger.info(`💪 Found custom benefit: ${customBenefit}`);
            }
        }
    }
    logger.info(`💪 Extracted ${benefits.length} benefits`);
    return benefits;
}
/**
 * Extract travel requirements from a job description
 */
export function extractTravelRequirements(description) {
    logger.info(`🔍 Extracting travel requirements from job description`);
    if (!description) {
        logger.info(`⚠️ No description provided for travel requirements extraction`);
        return undefined;
    }
    const lowerCaseDescription = description.toLowerCase();
    // Common patterns for travel requirements
    const travelPatterns = [
        /(\d+)\s*%\s*(?:to\s*\d+\s*%)?\s*travel/i,
        /travel\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?required\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?(?:required|expected)\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?(?:required|expected)\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*%/i,
        /travel\s*(?:is\s*)?(?:required|expected)\s*(?:up to|approximately|approx|around|about)?\s*(\d+)\s*percent/i,
    ];
    // Look for percentage-based travel requirements
    for (const pattern of travelPatterns) {
        const match = lowerCaseDescription.match(pattern);
        if (match && match[1]) {
            const percentage = parseInt(match[1], 10);
            if (!isNaN(percentage) && percentage >= 0 && percentage <= 100) {
                const result = `${percentage}%`;
                logger.info(`✈️ Extracted travel requirement: ${result}`);
                return result;
            }
        }
    }
    // Look for descriptive travel requirements
    const descriptiveTravelPatterns = [
        {
            term: "No Travel",
            patterns: ["no travel", "travel is not required", "0% travel"],
        },
        {
            term: "Minimal Travel",
            patterns: [
                "minimal travel",
                "occasional travel",
                "infrequent travel",
                "limited travel",
                "rare travel",
            ],
        },
        {
            term: "Moderate Travel",
            patterns: [
                "moderate travel",
                "some travel",
                "periodic travel",
                "regular travel",
            ],
        },
        {
            term: "Frequent Travel",
            patterns: [
                "frequent travel",
                "extensive travel",
                "heavy travel",
                "significant travel",
                "substantial travel",
            ],
        },
        {
            term: "Travel Required",
            patterns: [
                "travel required",
                "travel is required",
                "must be willing to travel",
                "ability to travel",
            ],
        },
    ];
    for (const { term, patterns } of descriptiveTravelPatterns) {
        for (const pattern of patterns) {
            if (lowerCaseDescription.includes(pattern)) {
                logger.info(`✈️ Extracted travel requirement: ${term}`);
                return term;
            }
        }
    }
    logger.info(`⚠️ No travel requirements found`);
    return undefined;
}
