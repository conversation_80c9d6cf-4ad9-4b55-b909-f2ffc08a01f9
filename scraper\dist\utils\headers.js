/**
 * Get random browser headers to help with fingerprinting evasion
 */
export function getRandomHeaders() {
    // Always use en-US as the language
    const languages = ["en-US,en;q=1.0"];
    const acceptHeaders = [
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
    ];
    const cacheControl = Math.random() > 0.5 ? "max-age=0" : "no-cache";
    return {
        "Accept-Language": languages[Math.floor(Math.random() * languages.length)],
        Accept: acceptHeaders[Math.floor(Math.random() * acceptHeaders.length)],
        "Cache-Control": cacheControl,
        "Sec-Ch-Ua": '"Google Chrome";v="120", "Chromium";v="120"',
        "Sec-Ch-Ua-Mobile": "?0",
        "Sec-Ch-Ua-Platform": '"Windows"',
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1",
    };
}
/**
 * Get a default user agent string
 * Using a completely standard user agent that matches real Chrome users
 */
export function getDefaultUserAgent() {
    return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
}
