// scraper/utils/executeCommand.ts
// Utility for executing commands with streaming output and timeout handling
import { spawn } from "child_process";
import { logger } from "./logger.js";
/**
 * Execute a command with streaming output and timeout handling
 * @param cwd Working directory for the command
 * @param command Command to execute
 * @param args Command arguments
 * @param jobName Name of the job (for logging)
 * @param maxDuration Maximum duration in milliseconds (optional)
 * @returns Promise that resolves when command completes
 */
export async function executeCommandStreaming(cwd, command, args, jobName, maxDuration) {
    const effectiveTimeout = maxDuration ?? 30 * 60 * 1000; // Default 30 minutes
    logger.info(`🔍 Starting job ${jobName} with timeout: ${effectiveTimeout / (60 * 1000)} minutes`);
    // Set environment variables
    const env = {
        ...process.env,
        RUNNING_FROM_SCRAPER: "true",
    };
    logger.info(`🔄 Executing command: ${command} ${args.join(" ")} in ${cwd}`);
    return new Promise((resolve, reject) => {
        let isResolved = false;
        let child;
        // Set up timeout
        const timeoutId = setTimeout(() => {
            if (!isResolved) {
                isResolved = true;
                logger.error(`⏰ Job ${jobName} timed out after ${effectiveTimeout / (60 * 1000)} minutes`);
                if (child) {
                    // Try graceful shutdown first
                    child.kill('SIGTERM');
                    // Force kill after 10 seconds if still running
                    setTimeout(() => {
                        if (child && !child.killed) {
                            logger.warn(`🔪 Force killing job ${jobName}`);
                            child.kill('SIGKILL');
                        }
                    }, 10000);
                }
                reject(new Error(`Job ${jobName} timed out after ${effectiveTimeout / (60 * 1000)} minutes`));
            }
        }, effectiveTimeout);
        try {
            logger.info(`🕐 Starting ${jobName} at ${new Date().toLocaleString()}`);
            child = spawn(command, args, {
                cwd,
                env,
                stdio: ["ignore", "pipe", "pipe"],
                shell: true,
            });
            // Handle stdout
            child.stdout?.on("data", (data) => {
                const output = data.toString().trim();
                if (output) {
                    logger.info(`📤 [${jobName}] ${output}`);
                }
            });
            // Handle stderr
            child.stderr?.on("data", (data) => {
                const output = data.toString().trim();
                if (output) {
                    logger.error(`📥 [${jobName}] ${output}`);
                }
            });
            // Handle process exit
            child.on("close", (code, signal) => {
                if (!isResolved) {
                    isResolved = true;
                    clearTimeout(timeoutId);
                    if (code === 0) {
                        logger.info(`✅ Job ${jobName} completed successfully`);
                        resolve();
                    }
                    else {
                        const errorMsg = signal
                            ? `Job ${jobName} was killed with signal ${signal}`
                            : `Job ${jobName} exited with code ${code}`;
                        logger.error(`❌ ${errorMsg}`);
                        reject(new Error(errorMsg));
                    }
                }
            });
            // Handle process errors
            child.on("error", (error) => {
                if (!isResolved) {
                    isResolved = true;
                    clearTimeout(timeoutId);
                    logger.error(`❌ Failed to start job ${jobName}:`, error);
                    reject(error);
                }
            });
        }
        catch (error) {
            if (!isResolved) {
                isResolved = true;
                clearTimeout(timeoutId);
                logger.error(`❌ Error setting up job ${jobName}:`, error);
                reject(error);
            }
        }
    });
}
