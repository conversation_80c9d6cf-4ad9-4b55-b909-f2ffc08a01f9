import { logger } from "./logger.js";
/**
 * Generate a random but realistic browser fingerprint
 */
export function generateRandomFingerprint() {
    // Operating systems - expanded list with versions
    const platforms = [
        "Win32",
        "Win64",
        "MacIntel",
        "Linux x86_64",
        "Windows NT 10.0",
        "Windows NT 6.3",
        "Windows NT 6.1",
        "Macintosh; Intel Mac OS X 10_15_7",
        "X11; Linux x86_64",
    ];
    // WebGL vendors
    const webGLVendors = [
        "Google Inc. (NVIDIA)",
        "Google Inc. (Intel)",
        "Google Inc. (AMD)",
        "Google Inc. (Apple)",
        "Intel Inc.",
        "NVIDIA Corporation",
        "AMD",
    ];
    // WebGL renderers
    const webGLRenderers = [
        "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11)",
        "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)",
        "ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11)",
        "Intel(R) Iris(R) Xe Graphics",
        "NVIDIA GeForce RTX 3070",
        "AMD Radeon RX 6800 XT",
    ];
    // Locales
    const locales = ["en-US", "en-GB", "en-CA", "en-AU"];
    // Timezones
    const timezones = [
        "America/New_York",
        "America/Los_Angeles",
        "America/Chicago",
        "America/Denver",
        "America/Phoenix",
        "Europe/London",
        "Europe/Paris",
        "Asia/Tokyo",
    ];
    // Generate random viewport (limited to max 960x960)
    const baseViewports = [
        { width: 800, height: 600 },
        { width: 960, height: 720 },
        { width: 960, height: 540 },
        { width: 800, height: 450 },
        { width: 960, height: 600 },
        { width: 900, height: 640 },
    ];
    const viewport = baseViewports[Math.floor(Math.random() * baseViewports.length)];
    // Add some random variation to viewport
    const widthVariation = Math.floor(Math.random() * 20) - 10; // +/- 10px
    const heightVariation = Math.floor(Math.random() * 20) - 10; // +/- 10px
    viewport.width += widthVariation;
    viewport.height += heightVariation;
    // Ensure dimensions don't exceed 960x960
    viewport.width = Math.min(viewport.width, 960);
    viewport.height = Math.min(viewport.height, 960);
    // Generate random hardware concurrency (CPU cores)
    const hardwareConcurrency = [2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16][Math.floor(Math.random() * 11)];
    // Generate random device memory (in GB)
    const deviceMemory = [2, 4, 4, 8, 8, 8, 16, 16, 32][Math.floor(Math.random() * 9)];
    // Generate random color depth
    const colorDepth = [24, 24, 30, 32][Math.floor(Math.random() * 4)];
    // Generate random do-not-track setting (weighted toward 0)
    const doNotTrack = ["0", "0", "0", "1", "unspecified"][Math.floor(Math.random() * 5)];
    // Generate random device scale factor (weighted toward common values)
    const deviceScaleFactor = [
        1, 1, 1, 1, 1, 1.25, 1.5, 1.5, 1.75, 2, 2, 2, 2.25, 2.5,
    ][Math.floor(Math.random() * 14)];
    return {
        platform: platforms[Math.floor(Math.random() * platforms.length)],
        webGLVendor: webGLVendors[Math.floor(Math.random() * webGLVendors.length)],
        webGLRenderer: webGLRenderers[Math.floor(Math.random() * webGLRenderers.length)],
        locale: locales[Math.floor(Math.random() * locales.length)],
        timezoneId: timezones[Math.floor(Math.random() * timezones.length)],
        viewport,
        hardwareConcurrency,
        deviceMemory,
        colorDepth,
        doNotTrack,
        deviceScaleFactor,
        touchPoints: Math.random() > 0.8 ? 5 : 0, // 20% chance of touch support
    };
}
/**
 * Apply advanced fingerprinting to a browser context
 */
export async function applyFingerprint(context, page, options) {
    // Add a small random delay before applying fingerprinting to make it less detectable
    await new Promise((resolve) => setTimeout(resolve, 100 + Math.floor(Math.random() * 300)));
    try {
        logger.info("🔍 Applying advanced browser fingerprinting...");
        // Apply JavaScript-based fingerprinting overrides
        await page.addInitScript(`
      // Override navigator properties
      function hookProperty(obj, prop, value) {
        try {
          Object.defineProperty(obj, prop, {
            get: function() { return value; },
            configurable: true
          });
        } catch (e) {
          // Ignore errors
        }
      }

      // Platform
      if ('${options.platform}') {
        hookProperty(Navigator.prototype, 'platform', '${options.platform}');
      }

      // Hardware concurrency
      if (${options.hardwareConcurrency}) {
        hookProperty(Navigator.prototype, 'hardwareConcurrency', ${options.hardwareConcurrency});
      }

      // Device memory
      if (${options.deviceMemory}) {
        hookProperty(Navigator.prototype, 'deviceMemory', ${options.deviceMemory});
      }

      // Do not track
      if ('${options.doNotTrack}') {
        hookProperty(Navigator.prototype, 'doNotTrack', '${options.doNotTrack}');
      }

      // WebGL fingerprinting protection
      const getParameterProxyHandler = {
        apply: function(target, thisArg, argumentsList) {
          const parameter = argumentsList[0];
          // Return consistent values for WebGL parameters
          if (parameter === 37445) {
            return '${options.webGLVendor}'; // UNMASKED_VENDOR_WEBGL
          }
          if (parameter === 37446) {
            return '${options.webGLRenderer}'; // UNMASKED_RENDERER_WEBGL
          }
          return target.apply(thisArg, argumentsList);
        }
      };

      // Apply the proxy to WebGL getParameter
      if (window.WebGLRenderingContext) {
        const prototype = WebGLRenderingContext.prototype;
        const getParameter = prototype.getParameter;
        prototype.getParameter = new Proxy(getParameter, getParameterProxyHandler);
      }

      // Canvas fingerprinting protection
      const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
      HTMLCanvasElement.prototype.toDataURL = function(type) {
        // Add subtle noise to canvas data for fingerprinting protection
        if (this.width === 16 && this.height === 16) {
          const context = this.getContext("2d");
          if (context) {
            const imageData = context.getImageData(0, 0, this.width, this.height);
            const data = imageData.data;
            for (let i = 0; i < data.length; i += 4) {
              // Only modify alpha channel slightly
              data[i + 3] = data[i + 3] > 0 ? data[i + 3] - 1 : data[i + 3];
            }
            context.putImageData(imageData, 0, 0);
          }
        }
        return originalToDataURL.apply(this, arguments);
      };

      // Hide automation flags
      delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
      delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
      delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

      // Override navigator.webdriver
      Object.defineProperty(navigator, 'webdriver', {
        get: () => false,
        configurable: true
      });
    `);
        logger.info("✅ Advanced fingerprinting applied successfully");
    }
    catch (error) {
        logger.warn(`⚠️ Error applying fingerprinting: ${error}`);
    }
}
